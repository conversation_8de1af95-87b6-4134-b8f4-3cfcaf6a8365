2025-07-07 23:29:55,164 - INFO - Setting up Chrome driver...
2025-07-07 23:30:00,203 - INFO - Chrome driver setup successful
2025-07-07 23:30:00,203 - INFO - Loading store mappings...
2025-07-07 23:30:00,209 - INFO - Loaded 2086 stores
2025-07-07 23:30:00,210 - INFO - Testing with store: 10498 (amberieu-en-bugey-01500)
2025-07-07 23:30:00,212 - INFO - Navigating to store 10498 (amberieu-en-bugey-01500)
2025-07-07 23:30:00,212 - INFO - Loading store page: https://www.intermarche.com/magasins/10498/amberieu-en-bugey-01500/infos-pratiques
2025-07-07 23:30:01,700 - INFO - Debug HTML saved: debug_browser_1_store_page_10498.html
2025-07-07 23:30:04,701 - INFO - Looking for 'Faire mes courses' button...
2025-07-07 23:30:33,309 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, 'Une connexion existante a dû être fermée par l’hôte distant', None, 10054, None))': /session/487d38a6c8a44ff55d5cf4845da9c652
2025-07-07 23:34:11,354 - INFO - Setting up Chrome driver...
2025-07-07 23:34:13,241 - INFO - Chrome driver setup successful
2025-07-07 23:34:13,241 - INFO - Loading store mappings...
2025-07-07 23:34:13,248 - INFO - Loaded 2086 stores
2025-07-07 23:34:13,249 - INFO - Testing with store: 10498 (amberieu-en-bugey-01500)
2025-07-07 23:34:13,250 - INFO - Navigating to store 10498 (amberieu-en-bugey-01500)
2025-07-07 23:34:13,251 - INFO - Loading store page: https://www.intermarche.com/magasins/10498/amberieu-en-bugey-01500/infos-pratiques
2025-07-07 23:34:15,664 - INFO - Waiting for page to load completely...
2025-07-07 23:34:23,667 - ERROR - Failed to save debug HTML: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409dea]
	(No symbol) [0x0x7ff7d23f5f15]
	(No symbol) [0x0x7ff7d241abf4]
	(No symbol) [0x0x7ff7d248fa85]
	(No symbol) [0x0x7ff7d24aff72]
	(No symbol) [0x0x7ff7d2488243]
	(No symbol) [0x0x7ff7d2451431]
	(No symbol) [0x0x7ff7d24521c3]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	GetHandleVerifier [0x0x7ff7d265fb14+112628]
	GetHandleVerifier [0x0x7ff7d265fcc9+113065]
	GetHandleVerifier [0x0x7ff7d2646c98+10616]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,671 - INFO - Looking for 'Faire mes courses' button...
2025-07-07 23:34:23,671 - INFO - Trying selector 1: //button[@aria-label='Faire mes courses']
2025-07-07 23:34:23,723 - ERROR - Error navigating to store: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409c1c]
	(No symbol) [0x0x7ff7d245055f]
	(No symbol) [0x0x7ff7d2488332]
	(No symbol) [0x0x7ff7d2482e53]
	(No symbol) [0x0x7ff7d2481f19]
	(No symbol) [0x0x7ff7d23d4b05]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	(No symbol) [0x0x7ff7d23d3b00]
	GetHandleVerifier [0x0x7ff7d2a45f38+4201496]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,725 - ERROR - Failed to save debug HTML: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409c1c]
	(No symbol) [0x0x7ff7d245055f]
	(No symbol) [0x0x7ff7d2488332]
	(No symbol) [0x0x7ff7d2482e53]
	(No symbol) [0x0x7ff7d2481f19]
	(No symbol) [0x0x7ff7d23d4b05]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	(No symbol) [0x0x7ff7d23d3b00]
	GetHandleVerifier [0x0x7ff7d2a45f38+4201496]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,726 - WARNING - Failed to navigate to store 10498, trying next store...
2025-07-07 23:34:23,726 - INFO - Testing with store: 11691 (arbent-01100)
2025-07-07 23:34:23,726 - INFO - Navigating to store 11691 (arbent-01100)
2025-07-07 23:34:23,727 - INFO - Loading store page: https://www.intermarche.com/magasins/11691/arbent-01100/infos-pratiques
2025-07-07 23:34:23,728 - ERROR - Error navigating to store: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409c1c]
	(No symbol) [0x0x7ff7d245055f]
	(No symbol) [0x0x7ff7d2488332]
	(No symbol) [0x0x7ff7d2482e53]
	(No symbol) [0x0x7ff7d2481f19]
	(No symbol) [0x0x7ff7d23d4b05]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	(No symbol) [0x0x7ff7d23d3b00]
	GetHandleVerifier [0x0x7ff7d2a45f38+4201496]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,730 - ERROR - Failed to save debug HTML: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409c1c]
	(No symbol) [0x0x7ff7d245055f]
	(No symbol) [0x0x7ff7d2488332]
	(No symbol) [0x0x7ff7d2482e53]
	(No symbol) [0x0x7ff7d2481f19]
	(No symbol) [0x0x7ff7d23d4b05]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	(No symbol) [0x0x7ff7d23d3b00]
	GetHandleVerifier [0x0x7ff7d2a45f38+4201496]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,730 - WARNING - Failed to navigate to store 11691, trying next store...
2025-07-07 23:34:23,730 - INFO - Testing with store: 05667 (beon-01350)
2025-07-07 23:34:23,731 - INFO - Navigating to store 05667 (beon-01350)
2025-07-07 23:34:23,731 - INFO - Loading store page: https://www.intermarche.com/magasins/05667/beon-01350/infos-pratiques
2025-07-07 23:34:23,732 - ERROR - Error navigating to store: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409c1c]
	(No symbol) [0x0x7ff7d245055f]
	(No symbol) [0x0x7ff7d2488332]
	(No symbol) [0x0x7ff7d2482e53]
	(No symbol) [0x0x7ff7d2481f19]
	(No symbol) [0x0x7ff7d23d4b05]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	(No symbol) [0x0x7ff7d23d3b00]
	GetHandleVerifier [0x0x7ff7d2a45f38+4201496]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,733 - ERROR - Failed to save debug HTML: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7d2656f95+76917]
	GetHandleVerifier [0x0x7ff7d2656ff0+77008]
	(No symbol) [0x0x7ff7d2409c1c]
	(No symbol) [0x0x7ff7d245055f]
	(No symbol) [0x0x7ff7d2488332]
	(No symbol) [0x0x7ff7d2482e53]
	(No symbol) [0x0x7ff7d2481f19]
	(No symbol) [0x0x7ff7d23d4b05]
	GetHandleVerifier [0x0x7ff7d292d2cd+3051437]
	GetHandleVerifier [0x0x7ff7d2927923+3028483]
	GetHandleVerifier [0x0x7ff7d29458bd+3151261]
	GetHandleVerifier [0x0x7ff7d267185e+185662]
	GetHandleVerifier [0x0x7ff7d267971f+218111]
	(No symbol) [0x0x7ff7d23d3b00]
	GetHandleVerifier [0x0x7ff7d2a45f38+4201496]
	BaseThreadInitThunk [0x0x7fffcc5fdbe7+23]
	RtlUserThreadStart [0x0x7fffcd45fbec+44]

2025-07-07 23:34:23,734 - WARNING - Failed to navigate to store 05667, trying next store...
2025-07-07 23:34:25,803 - INFO - Browser driver closed
