#!/usr/bin/env python3
"""
Test EAN-based API directly across multiple stores using known EAN codes
"""

import requests
import json
import time
from datetime import datetime

def test_ean_direct_api(store_id, ean_codes):
    """Test EAN-based API directly without full context establishment"""
    session = requests.Session()
    
    # API headers from successful tests
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'x-red-version': '3',
        'x-itm-device-fp': 'desktop',
        'x-itm-session-id': f'session_{int(time.time())}',
        'x-custom-hash': f'{store_id}-disconnected-desktop',
        'Origin': 'https://www.intermarche.com',
        'Referer': f'https://www.intermarche.com/magasins/{store_id}/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    try:
        # Test byEans API directly
        byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{store_id}/products/byEans"
        byeans_data = {"eans": ean_codes}
        
        response = session.post(byeans_url, headers=api_headers, json=byeans_data, timeout=10)
        
        if response.status_code != 200:
            return False, f"ByEans API failed: {response.status_code}", []
        
        result = response.json()
        products = result.get('products', [])
        
        return True, f"Success: {len(products)} products found", products
        
    except Exception as e:
        return False, f"Error: {str(e)}", []

def main():
    """Test EAN codes across multiple stores"""
    
    # Load store mappings
    try:
        with open('store_mappings_clean.csv', 'r', encoding='utf-8') as f:
            store_mappings = []
            for line in f:
                line = line.strip()
                if line and ',' in line:
                    store_id, city_code = line.split(',', 1)
                    store_mappings.append((store_id, city_code))
    except FileNotFoundError:
        print("Error: store_mappings_clean.csv not found.")
        return
    
    print(f"Loaded {len(store_mappings)} store mappings")
    
    # Known EAN codes from our successful tests (from comprehensive_ean_results)
    test_eans = [
        "3760074380145",  # Crème dessert vanille chocolat
        "3760074380152",  # Crème dessert 3 chocolats  
        "3760074380169",  # Another dessert product
        "8001505005707",  # Pâte à tartiner aux noisettes
        "3228857000087"   # Mayonnaise Nature
    ]
    
    print(f"Testing {len(test_eans)} EAN codes across stores")
    
    # Test a sample of stores including our known working one
    known_working = [('12118', 'marseille-13004')]
    sample_stores = known_working + store_mappings[:19]  # Test 20 stores total
    
    results = {
        'successful_stores': [],
        'failed_stores': [],
        'store_details': {}
    }
    
    for i, (store_id, city_code) in enumerate(sample_stores, 1):
        print(f"\n=== Testing Store {i}/{len(sample_stores)}: {store_id} ({city_code}) ===")
        
        success, message, products = test_ean_direct_api(store_id, test_eans)
        print(f"Result: {message}")
        
        if success and products:
            results['successful_stores'].append({
                'store_id': store_id,
                'city_code': city_code,
                'products_found': len(products)
            })
            
            # Store detailed results
            results['store_details'][store_id] = {
                'city_code': city_code,
                'products_found': len(products),
                'products': []
            }
            
            # Show sample products with pricing
            for product in products[:3]:  # Show first 3 products
                product_info = {
                    'name': product.get('name', 'Unknown'),
                    'price': product.get('price', {}).get('value', 'N/A'),
                    'stock': product.get('stock', {}).get('quantity', 'N/A'),
                    'ean': product.get('ean', 'N/A')
                }
                results['store_details'][store_id]['products'].append(product_info)
                
                price_str = f"€{product_info['price']}" if product_info['price'] != 'N/A' else 'No price'
                stock_str = f"Stock: {product_info['stock']}" if product_info['stock'] != 'N/A' else 'No stock info'
                print(f"  📦 {product_info['name']} - {price_str} ({stock_str})")
        else:
            results['failed_stores'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': message
            })
        
        # Small delay between requests
        time.sleep(2)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"ean_across_stores_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total stores tested: {len(sample_stores)}")
    print(f"Successful stores: {len(results['successful_stores'])}")
    print(f"Failed stores: {len(results['failed_stores'])}")
    print(f"Success rate: {len(results['successful_stores'])/len(sample_stores)*100:.1f}%")
    print(f"\nResults saved to: {results_file}")
    
    if results['successful_stores']:
        print(f"\nWorking stores:")
        for store in results['successful_stores']:
            print(f"  - {store['store_id']} ({store['city_code']}): {store['products_found']} products")
    
    # Show stores with different pricing for same products
    if len(results['successful_stores']) > 1:
        print(f"\n=== PRICING COMPARISON ===")
        # Compare first product across stores
        first_ean = test_eans[0]
        print(f"Comparing prices for EAN {first_ean} across stores:")
        
        for store_id, details in results['store_details'].items():
            if details['products']:
                for product in details['products']:
                    if product['ean'] == first_ean:
                        price_str = f"€{product['price']}" if product['price'] != 'N/A' else 'No price'
                        print(f"  Store {store_id}: {price_str}")
                        break

if __name__ == "__main__":
    main()
