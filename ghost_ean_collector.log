2025-07-07 23:40:19,133 - INFO - ============================================================
2025-07-07 23:40:19,134 - INFO - GHOST EAN COLLECTOR - AD<PERSON>NCED CAPTCHA BYPASS
2025-07-07 23:40:19,134 - INFO - ============================================================
2025-07-07 23:40:19,201 - INFO - Setting up Ghost Chrome driver...
2025-07-07 23:40:19,206 - INFO - Using User Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1
2025-07-07 23:40:20,596 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-07-07 23:40:24,799 - ERROR - Failed to setup Ghost Chrome driver: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: detach
Stacktrace:
	GetHandleVerifier [0x0x3b4553+62419]
	GetHandleVerifier [0x0x3b4594+62484]
	(No symbol) [0x0x1f2133]
	(No symbol) [0x0x219723]
	(No symbol) [0x0x21aeb0]
	(No symbol) [0x0x215fea]
	(No symbol) [0x0x269832]
	(No symbol) [0x0x26931c]
	(No symbol) [0x0x26aa20]
	(No symbol) [0x0x26a82a]
	(No symbol) [0x0x25f266]
	(No symbol) [0x0x22e852]
	(No symbol) [0x0x22f6f4]
	GetHandleVerifier [0x0x624833+2619059]
	GetHandleVerifier [0x0x61fc4a+2599626]
	GetHandleVerifier [0x0x3db0fa+221050]
	GetHandleVerifier [0x0x3cb378+156152]
	GetHandleVerifier [0x0x3d1d2d+183213]
	GetHandleVerifier [0x0x3bc438+94904]
	GetHandleVerifier [0x0x3bc5c2+95298]
	GetHandleVerifier [0x0x3a771a+9626]
	BaseThreadInitThunk [0x0x76725d49+25]
	RtlInitializeExceptionChain [0x0x77e3cebb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77e3ce41+561]

2025-07-07 23:40:24,801 - ERROR - Failed to setup driver
2025-07-07 23:41:35,694 - INFO - ============================================================
2025-07-07 23:41:35,694 - INFO - GHOST EAN COLLECTOR - ADVANCED CAPTCHA BYPASS
2025-07-07 23:41:35,695 - INFO - ============================================================
2025-07-07 23:41:35,754 - INFO - Setting up Ghost Chrome driver...
2025-07-07 23:41:35,761 - INFO - Using User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-07 23:41:37,223 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-07-07 23:41:40,135 - ERROR - Failed to setup Ghost Chrome driver: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa74553+62419]
	GetHandleVerifier [0x0xa74594+62484]
	(No symbol) [0x0x8b2133]
	(No symbol) [0x0x8d9723]
	(No symbol) [0x0x8daeb0]
	(No symbol) [0x0x8d5fea]
	(No symbol) [0x0x929832]
	(No symbol) [0x0x92931c]
	(No symbol) [0x0x92aa20]
	(No symbol) [0x0x92a82a]
	(No symbol) [0x0x91f266]
	(No symbol) [0x0x8ee852]
	(No symbol) [0x0x8ef6f4]
	GetHandleVerifier [0x0xce4833+2619059]
	GetHandleVerifier [0x0xcdfc4a+2599626]
	GetHandleVerifier [0x0xa9b0fa+221050]
	GetHandleVerifier [0x0xa8b378+156152]
	GetHandleVerifier [0x0xa91d2d+183213]
	GetHandleVerifier [0x0xa7c438+94904]
	GetHandleVerifier [0x0xa7c5c2+95298]
	GetHandleVerifier [0x0xa6771a+9626]
	BaseThreadInitThunk [0x0x76725d49+25]
	RtlInitializeExceptionChain [0x0x77e3cebb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77e3ce41+561]

2025-07-07 23:41:40,136 - ERROR - Failed to setup driver
2025-07-07 23:42:23,150 - INFO - ============================================================
2025-07-07 23:42:23,155 - INFO - GHOST EAN COLLECTOR - ADVANCED CAPTCHA BYPASS
2025-07-07 23:42:23,155 - INFO - ============================================================
2025-07-07 23:42:23,235 - INFO - Setting up Ghost Chrome driver...
2025-07-07 23:42:23,243 - INFO - Using User Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1
2025-07-07 23:42:25,113 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-07-07 23:42:26,888 - INFO - Stealth mode applied successfully
2025-07-07 23:42:26,902 - INFO - Ghost Chrome driver setup successful (Session #1)
2025-07-07 23:42:26,906 - INFO - Loaded 2086 stores
2025-07-07 23:42:26,906 - INFO - Loaded 2086 stores
2025-07-07 23:42:26,906 - INFO - Testing with store: ﻿10498 (amberieu-en-bugey-01500)
2025-07-07 23:42:26,907 - INFO - Navigating to store ﻿10498 (amberieu-en-bugey-01500)
2025-07-07 23:42:26,908 - INFO - Loading store page: https://www.intermarche.com/magasins/﻿10498/amberieu-en-bugey-01500/infos-pratiques (Attempt 1)
2025-07-07 23:42:33,636 - INFO - Checking for captcha...
2025-07-07 23:42:33,638 - ERROR - Error navigating to store (attempt 1): Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xa14553+62419]
	GetHandleVerifier [0x0xa14594+62484]
	(No symbol) [0x0x851f90]
	(No symbol) [0x0x88db48]
	(No symbol) [0x0x8bf326]
	(No symbol) [0x0x8baf15]
	(No symbol) [0x0x8ba496]
	(No symbol) [0x0x823a45]
	(No symbol) [0x0x823f9e]
	(No symbol) [0x0x82442d]
	GetHandleVerifier [0x0xc84833+2619059]
	GetHandleVerifier [0x0xc7fc4a+2599626]
	GetHandleVerifier [0x0xa3b0fa+221050]
	GetHandleVerifier [0x0xa2b378+156152]
	GetHandleVerifier [0x0xa31d2d+183213]
	(No symbol) [0x0x823710]
	(No symbol) [0x0x822f1d]
	GetHandleVerifier [0x0xdb8b8c+3881484]
	BaseThreadInitThunk [0x0x76725d49+25]
	RtlInitializeExceptionChain [0x0x77e3cebb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77e3ce41+561]

2025-07-07 23:42:33,640 - ERROR - Failed to save debug HTML: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xa14553+62419]
	GetHandleVerifier [0x0xa14594+62484]
	(No symbol) [0x0x851f90]
	(No symbol) [0x0x88db48]
	(No symbol) [0x0x8bf326]
	(No symbol) [0x0x8baf15]
	(No symbol) [0x0x8ba496]
	(No symbol) [0x0x823a45]
	(No symbol) [0x0x823f9e]
	(No symbol) [0x0x82442d]
	GetHandleVerifier [0x0xc84833+2619059]
	GetHandleVerifier [0x0xc7fc4a+2599626]
	GetHandleVerifier [0x0xa3b0fa+221050]
	GetHandleVerifier [0x0xa2b378+156152]
	GetHandleVerifier [0x0xa31d2d+183213]
	(No symbol) [0x0x823710]
	(No symbol) [0x0x822f1d]
	GetHandleVerifier [0x0xdb8b8c+3881484]
	BaseThreadInitThunk [0x0x76725d49+25]
	RtlInitializeExceptionChain [0x0x77e3cebb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77e3ce41+561]

2025-07-07 23:42:33,640 - INFO - Restarting session for next attempt...
2025-07-07 23:42:33,640 - INFO - Restarting session with new fingerprint...
2025-07-07 23:42:35,717 - INFO - Collection interrupted by user
2025-07-07 23:42:36,223 - INFO - Browser driver closed
