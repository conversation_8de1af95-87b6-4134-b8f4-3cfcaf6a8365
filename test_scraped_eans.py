#!/usr/bin/env python3
"""
Test scraped EANs across multiple Intermarché stores
"""

import requests
import json
import time
import logging
from datetime import datetime
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

class ScrapedEANTester:
    def __init__(self):
        self.base_url = "https://www.intermarche.com"
        self.session = requests.Session()
        
        # Headers from successful HAR requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'fr-FR,fr;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }

    def load_scraped_eans(self, filename="intermarche_eans.json"):
        """Load EANs from our scraped data"""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
                eans = data.get('eans', [])
                log.info(f"📥 Loaded {len(eans)} EANs from {filename}")
                return eans
        except Exception as e:
            log.error(f"❌ Failed to load EANs from {filename}: {e}")
            return []

    def load_store_mappings(self, filename="store_mappings_clean.csv"):
        """Load store mappings from CSV"""
        try:
            stores = []
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            store_id = parts[0]
                            city_code = parts[1]
                            stores.append({
                                'store_id': store_id,
                                'name': city_code.split('-')[0].title(),
                                'city': city_code,
                                'address': city_code
                            })
            log.info(f"📥 Loaded {len(stores)} stores from {filename}")
            return stores
        except Exception as e:
            log.error(f"❌ Failed to load stores from {filename}: {e}")
            return []

    def test_eans_for_store(self, store_id, store_name, eans, max_eans=100):
        """Test EANs for a specific store"""
        log.info(f"🏪 Testing store {store_id} ({store_name}) with {min(len(eans), max_eans)} EANs")
        
        # Use a subset of EANs for faster testing
        test_eans = eans[:max_eans] if len(eans) > max_eans else eans
        
        successful_products = []
        batch_size = 20  # API handles up to 20 EANs per request
        
        for i in range(0, len(test_eans), batch_size):
            batch_eans = test_eans[i:i+batch_size]
            batch_num = (i // batch_size) + 1
            
            try:
                api_headers = self.headers.copy()
                api_headers.update({
                    'Accept': '*/*',
                    'Content-Type': 'application/json',
                    'Origin': self.base_url,
                    'Referer': f'{self.base_url}/recherche/lait',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-origin',
                    'x-is-server': 'false'
                })
                
                byeans_data = {"eans": batch_eans}
                
                response = self.session.post(
                    f"{self.base_url}/api/service/produits/v3/stores/{store_id}/products/byEans",
                    json=byeans_data,
                    headers=api_headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data and isinstance(data, list):
                        successful_products.extend(data)
                        log.info(f"   ✅ Batch {batch_num}: {len(data)} products found")
                    else:
                        log.warning(f"   ⚠️ Batch {batch_num}: No products in response")
                else:
                    log.warning(f"   ❌ Batch {batch_num}: API failed with status {response.status_code}")
                
                time.sleep(0.5)  # Small delay between requests
                
            except Exception as e:
                log.error(f"   ❌ Batch {batch_num}: Error - {e}")
                continue
        
        success_rate = (len(successful_products) / len(test_eans)) * 100 if test_eans else 0
        log.info(f"   📊 Store {store_id}: {len(successful_products)}/{len(test_eans)} products found ({success_rate:.1f}% success rate)")
        
        return {
            'store_id': store_id,
            'store_name': store_name,
            'eans_tested': len(test_eans),
            'products_found': len(successful_products),
            'success_rate': success_rate,
            'products': successful_products
        }

    def test_multiple_stores(self, num_stores=50, max_eans_per_store=100):
        """Test scraped EANs across multiple stores"""
        log.info(f"🚀 Starting multi-store test with {num_stores} stores")
        log.info("=" * 60)
        
        # Load scraped EANs
        eans = self.load_scraped_eans()
        if not eans:
            log.error("❌ No EANs to test")
            return
        
        # Load store mappings
        stores = self.load_store_mappings()
        if not stores:
            log.error("❌ No stores to test")
            return
        
        # Add some known working stores first
        known_working_stores = [
            {'store_id': '11621', 'name': 'Villeneuve-Loubet', 'city': 'villeneuve-loubet-06270', 'address': 'villeneuve-loubet-06270'},
            {'store_id': '12118', 'name': 'Marseille', 'city': 'marseille-13004', 'address': 'marseille-13004'}
        ]

        # Select random stores for testing, but include known working ones
        available_stores = [s for s in stores if s['store_id'] not in ['11621', '12118']]
        random_stores = random.sample(available_stores, min(num_stores - len(known_working_stores), len(available_stores)))
        test_stores = known_working_stores + random_stores

        log.info(f"🎯 Selected {len(test_stores)} stores for testing (including {len(known_working_stores)} known working stores)")
        
        results = []
        
        for i, store in enumerate(test_stores, 1):
            store_id = store.get('store_id')
            store_name = f"{store.get('name', 'Unknown')} - {store.get('city', 'Unknown')}"
            
            log.info(f"\n[{i}/{len(test_stores)}] Testing store {store_id}")
            
            result = self.test_eans_for_store(store_id, store_name, eans, max_eans_per_store)
            results.append(result)
            
            # Small delay between stores
            time.sleep(1)
        
        # Analyze results
        self.analyze_results(results, eans)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraped_eans_test_results_{timestamp}.json"
        
        final_results = {
            "test_summary": {
                "total_eans": len(eans),
                "stores_tested": len(test_stores),
                "max_eans_per_store": max_eans_per_store
            },
            "store_results": results
        }
        
        with open(filename, 'w') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        log.info(f"\n💾 Results saved to {filename}")
        return final_results

    def analyze_results(self, results, original_eans):
        """Analyze test results"""
        log.info("\n" + "=" * 60)
        log.info("📊 MULTI-STORE TEST ANALYSIS")
        log.info("=" * 60)
        
        total_stores = len(results)
        total_eans = len(original_eans)
        
        # Calculate statistics
        success_rates = [r['success_rate'] for r in results]
        avg_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0
        
        products_found = [r['products_found'] for r in results]
        avg_products_per_store = sum(products_found) / len(products_found) if products_found else 0
        
        # Find best and worst performing stores
        best_store = max(results, key=lambda x: x['success_rate']) if results else None
        worst_store = min(results, key=lambda x: x['success_rate']) if results else None
        
        log.info(f"🔍 Total EANs scraped: {total_eans}")
        log.info(f"🏪 Stores tested: {total_stores}")
        log.info(f"📈 Average success rate: {avg_success_rate:.1f}%")
        log.info(f"🛍️ Average products per store: {avg_products_per_store:.1f}")
        
        if best_store:
            log.info(f"🏆 Best performing store: {best_store['store_id']} ({best_store['success_rate']:.1f}%)")
        
        if worst_store:
            log.info(f"📉 Worst performing store: {worst_store['store_id']} ({worst_store['success_rate']:.1f}%)")
        
        # Count stores by success rate ranges
        excellent = sum(1 for r in results if r['success_rate'] >= 80)
        good = sum(1 for r in results if 60 <= r['success_rate'] < 80)
        fair = sum(1 for r in results if 40 <= r['success_rate'] < 60)
        poor = sum(1 for r in results if r['success_rate'] < 40)
        
        log.info(f"\n📊 Success rate distribution:")
        log.info(f"   🟢 Excellent (≥80%): {excellent} stores")
        log.info(f"   🟡 Good (60-79%): {good} stores")
        log.info(f"   🟠 Fair (40-59%): {fair} stores")
        log.info(f"   🔴 Poor (<40%): {poor} stores")

if __name__ == "__main__":
    tester = ScrapedEANTester()
    results = tester.test_multiple_stores(num_stores=10, max_eans_per_store=50)  # Reduced for faster testing
