#!/usr/bin/env python3
"""
Debug store differences using EXACT sequence from working comprehensive test
"""

import requests
import json
import time
from datetime import datetime

def test_store_exact_sequence(store_id, city_code, store_name="Unknown"):
    """Test using EXACT sequence from comprehensive_ean_test.py"""
    
    print(f"\n🔍 DEBUGGING STORE {store_id} ({city_code}) - {store_name}")
    print("=" * 60)
    
    session = requests.Session()
    
    # EXACT headers from comprehensive test
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    base_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}"
    
    results = {
        'store_id': store_id,
        'city_code': city_code,
        'store_name': store_name,
        'steps': {}
    }
    
    try:
        # Step 1: Store page (EXACT from comprehensive test)
        print("📍 Step 1: Accessing store page...")
        store_url = f"{base_url}/infos-pratiques"
        print(f"   URL: {store_url}")
        
        store_response = session.get(store_url, headers=headers, timeout=30)
        print(f"   Status: {store_response.status_code}")
        
        results['steps']['store_page'] = {
            'url': store_url,
            'status': store_response.status_code,
            'success': store_response.status_code == 200
        }
        
        if store_response.status_code != 200:
            print(f"   ❌ FAILED: Store page returned {store_response.status_code}")
            return results
        
        print("   ✅ Store page OK")
        
        # EXACT delay from comprehensive test
        time.sleep(1)
        
        # Step 2: GET accueil (EXACT from comprehensive test)
        print("🏠 Step 2: GET accueil...")
        get_accueil_headers = headers.copy()
        get_accueil_headers['Referer'] = store_url
        
        accueil_url = f"{base_url}/accueil"
        print(f"   URL: {accueil_url}")
        
        get_accueil_response = session.get(accueil_url, headers=get_accueil_headers, timeout=30)
        print(f"   Status: {get_accueil_response.status_code}")
        
        results['steps']['accueil_get'] = {
            'url': accueil_url,
            'status': get_accueil_response.status_code,
            'success': get_accueil_response.status_code == 200
        }
        
        if get_accueil_response.status_code != 200:
            print(f"   ❌ FAILED: Accueil GET returned {get_accueil_response.status_code}")
            return results
        
        print("   ✅ Accueil GET OK")
        
        # EXACT delay from comprehensive test
        time.sleep(1)
        
        # Step 3: POST accueil (EXACT from comprehensive test)
        print("🚀 Step 3: POST accueil...")
        post_accueil_headers = headers.copy()
        post_accueil_headers.update({
            'Accept': 'text/x-component',
            'Content-Type': 'text/plain;charset=UTF-8',
            'next-action': '0021d05dbc1bd15b63b38f154a7cf55a9cc8fea62c',  # EXACT from comprehensive test
            'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2Faccueil%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D',  # EXACT
            'Origin': base_url,
            'Referer': f'{base_url}/accueil',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        # EXACT POST data from comprehensive test
        post_data = '[]'  # NOT the store_id like we were doing!
        print(f"   POST data: {post_data}")
        
        post_accueil_response = session.post(accueil_url, headers=post_accueil_headers, data=post_data, timeout=30)
        print(f"   Status: {post_accueil_response.status_code}")
        
        results['steps']['accueil_post'] = {
            'status': post_accueil_response.status_code,
            'success': post_accueil_response.status_code == 200
        }
        
        if post_accueil_response.status_code != 200:
            print(f"   ❌ FAILED: Accueil POST returned {post_accueil_response.status_code}")
            return results
        
        print("   ✅ Accueil POST OK")
        
        # Step 4: Test suggestion API (EXACT headers from comprehensive test)
        print("🔍 Step 4: Testing suggestion API...")
        api_headers = headers.copy()
        api_headers.update({
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'Origin': base_url,
            'Referer': f'{base_url}/accueil',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',  # EXACT from comprehensive test
            'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',  # EXACT
            'x-red-device': 'red_fo_desktop',
            'x-red-version': '3',
            'x-service-name': 'produits',
            'x-optional-oauth': 'true',
            'x-is-server': 'false'
        })
        
        suggestion_url = f"https://www.intermarche.com/api/service/produits/v1/pdvs/{store_id}/products/suggestion"
        suggestion_data = {
            "query": "lait",
            "category": "4653",
            "limit": 5
        }
        
        print(f"   URL: {suggestion_url}")
        print(f"   Data: {suggestion_data}")
        
        # EXACT delay from comprehensive test
        time.sleep(0.5)
        
        response = session.post(suggestion_url, headers=api_headers, json=suggestion_data, timeout=30)
        print(f"   Status: {response.status_code}")
        
        results['steps']['suggestion_api'] = {
            'url': suggestion_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code == 200:
            result = response.json()
            suggestions = result.get('suggestions', [])
            print(f"   ✅ Suggestion API OK - Got {len(suggestions)} suggestions")
            results['steps']['suggestion_api']['suggestions_count'] = len(suggestions)
            
            if suggestions:
                # Step 5: Test byEans API (EXACT headers from comprehensive test)
                print("🛒 Step 5: Testing byEans API...")
                ean_codes = []
                for suggestion in suggestions[:3]:
                    if 'ean' in suggestion:
                        ean_codes.append(suggestion['ean'])
                
                if ean_codes:
                    byeans_headers = headers.copy()
                    byeans_headers.update({
                        'Accept': '*/*',
                        'Content-Type': 'application/json',
                        'Origin': base_url,
                        'Referer': f'{base_url}/accueil',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                        'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                        'x-red-device': 'red_fo_desktop',
                        'x-red-version': '3',
                        'x-service-name': 'produits',
                        'x-optional-oauth': 'true',
                        'x-is-server': 'false'
                    })
                    
                    byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{store_id}/products/byEans"
                    byeans_data = {"eans": ean_codes}
                    
                    print(f"   URL: {byeans_url}")
                    print(f"   EANs: {ean_codes}")
                    
                    # EXACT delay from comprehensive test
                    time.sleep(0.5)
                    
                    response = session.post(byeans_url, headers=byeans_headers, json=byeans_data, timeout=30)
                    print(f"   Status: {response.status_code}")
                    
                    results['steps']['byeans_api'] = {
                        'url': byeans_url,
                        'status': response.status_code,
                        'success': response.status_code == 200
                    }
                    
                    if response.status_code == 200:
                        result = response.json()
                        products = result.get('products', [])
                        print(f"   ✅ ByEans API OK - Got {len(products)} products")
                        results['steps']['byeans_api']['products_count'] = len(products)
                        
                        # Show sample products
                        for i, product in enumerate(products[:2]):
                            name = product.get('libelle', 'Unknown')
                            price = product.get('prix', 'N/A')
                            print(f"      📦 {name} - €{price}")
                    else:
                        print(f"   ❌ ByEans API failed: {response.status_code}")
                else:
                    print("   ⚠️ No EAN codes found in suggestions")
            else:
                print("   ⚠️ No suggestions returned")
        else:
            print(f"   ❌ Suggestion API failed: {response.status_code}")
        
        return results
        
    except Exception as e:
        print(f"   💥 ERROR: {str(e)}")
        results['error'] = str(e)
        return results

def main():
    """Debug different stores using EXACT sequence from working comprehensive test"""
    
    print("Testing stores using EXACT sequence from comprehensive_ean_test.py")
    
    # Test stores: our known working one + a few others
    test_stores = [
        ('12118', 'marseille-13004', 'Super Marseille - KNOWN WORKING'),
        ('11691', 'arbent-01100', 'Arbent Store'),
    ]
    
    all_results = []
    
    for i, (store_id, city_code, store_name) in enumerate(test_stores, 1):
        print(f"\n{'='*80}")
        print(f"TESTING STORE {i}/{len(test_stores)}")
        
        result = test_store_exact_sequence(store_id, city_code, store_name)
        all_results.append(result)
        
        # Long delay between stores
        if i < len(test_stores):
            print(f"\n⏳ Waiting 15 seconds before next store...")
            time.sleep(15)
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"store_debug_corrected_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n{'='*80}")
    print("SUMMARY OF RESULTS")
    print(f"{'='*80}")
    
    for result in all_results:
        store_id = result['store_id']
        city_code = result['city_code']
        store_name = result['store_name']
        
        print(f"\n🏪 {store_name} ({store_id})")
        
        steps = result.get('steps', {})
        for step_name, step_data in steps.items():
            status = "✅" if step_data.get('success', False) else "❌"
            print(f"   {status} {step_name}: {step_data.get('status', 'N/A')}")
    
    print(f"\nDetailed results saved to: {results_file}")

if __name__ == "__main__":
    main()
