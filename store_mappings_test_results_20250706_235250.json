{"successful_stores": [], "failed_stores": ["12118", "﻿10498", "11691", "05667", "10823", "10525", "06877", "10242", "10113", "01590"], "context_failures": [{"store_id": "﻿10498", "city_code": "amberieu-en-bugey-01500", "error": "Store page failed with status 403"}], "api_failures": [{"store_id": "12118", "city_code": "marseille-13004", "error": "Suggestion API failed with status 400"}, {"store_id": "11691", "city_code": "arbent-01100", "error": "Suggestion API failed with status 400"}, {"store_id": "05667", "city_code": "beon-01350", "error": "Suggestion API failed with status 400"}, {"store_id": "10823", "city_code": "beynost-01700", "error": "Suggestion API failed with status 400"}, {"store_id": "10525", "city_code": "bourg-en-bresse-01000", "error": "Suggestion API failed with status 400"}, {"store_id": "06877", "city_code": "briord-01470", "error": "Suggestion API failed with status 400"}, {"store_id": "10242", "city_code": "chatillon-sur-chalaronne-01400", "error": "Suggestion API failed with status 400"}, {"store_id": "10113", "city_code": "chazey-bons-01300", "error": "Suggestion API failed with status 400"}, {"store_id": "01590", "city_code": "feillens-01570", "error": "Suggestion API failed with status 400"}]}