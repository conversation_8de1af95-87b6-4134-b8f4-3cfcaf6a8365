#!/usr/bin/env python3
"""
Analyze price variations across stores for the same products
"""

import json
from collections import defaultdict
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def analyze_price_variations(filename="scraped_eans_multistore_results_20250708_175438.json"):
    """Analyze price variations for the same products across different stores"""
    
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
    except Exception as e:
        log.error(f"❌ Failed to load results file: {e}")
        return
    
    log.info(f"🔍 Analyzing price variations from {filename}")
    
    # Group products by EAN across all stores
    products_by_ean = defaultdict(list)
    
    for store_result in data['store_results']:
        store_id = store_result['store_id']
        store_name = store_result['store_name']
        
        if store_result['status'] == 'success' and 'products' in store_result:
            for product in store_result['products']:
                ean = product.get('produitEan13') or product.get('idProduit')
                if ean:
                    products_by_ean[ean].append({
                        'store_id': store_id,
                        'store_name': store_name,
                        'product_name': product.get('libelle', 'Unknown'),
                        'price': product.get('prix', 0),
                        'stock': product.get('stock', 0),
                        'marque': product.get('marque', 'Unknown')
                    })
    
    log.info(f"📊 Found {len(products_by_ean)} unique products across stores")
    
    # Analyze price variations
    price_variations = []
    
    for ean, product_instances in products_by_ean.items():
        if len(product_instances) > 1:  # Only analyze products found in multiple stores
            prices = [p['price'] for p in product_instances]
            min_price = min(prices)
            max_price = max(prices)
            price_diff = max_price - min_price
            price_variation_percent = (price_diff / min_price * 100) if min_price > 0 else 0
            
            if price_diff > 0:  # Only include products with actual price differences
                price_variations.append({
                    'ean': ean,
                    'product_name': product_instances[0]['product_name'],
                    'marque': product_instances[0]['marque'],
                    'stores_count': len(product_instances),
                    'min_price': min_price,
                    'max_price': max_price,
                    'price_diff': price_diff,
                    'variation_percent': price_variation_percent,
                    'stores': product_instances
                })
    
    # Sort by price variation percentage (highest first)
    price_variations.sort(key=lambda x: x['variation_percent'], reverse=True)
    
    log.info(f"🎯 Found {len(price_variations)} products with price variations")
    
    # Display top price variations
    log.info("\n" + "=" * 80)
    log.info("📈 TOP PRICE VARIATIONS ACROSS STORES")
    log.info("=" * 80)
    
    for i, variation in enumerate(price_variations[:10], 1):
        log.info(f"\n{i}. {variation['marque']} - {variation['product_name']}")
        log.info(f"   EAN: {variation['ean']}")
        log.info(f"   💰 Price range: €{variation['min_price']:.2f} - €{variation['max_price']:.2f}")
        log.info(f"   📊 Difference: €{variation['price_diff']:.2f} ({variation['variation_percent']:.1f}%)")
        log.info(f"   🏪 Found in {variation['stores_count']} stores:")
        
        for store in variation['stores']:
            log.info(f"      - {store['store_name']} ({store['store_id']}): €{store['price']:.2f} (Stock: {store['stock']})")
    
    # Statistics
    if price_variations:
        total_variations = len(price_variations)
        avg_variation = sum(v['variation_percent'] for v in price_variations) / total_variations
        max_variation = max(v['variation_percent'] for v in price_variations)
        
        # Count significant variations
        significant_variations = [v for v in price_variations if v['variation_percent'] >= 10]
        very_significant_variations = [v for v in price_variations if v['variation_percent'] >= 20]
        
        log.info("\n" + "=" * 80)
        log.info("📊 PRICE VARIATION STATISTICS")
        log.info("=" * 80)
        log.info(f"📈 Total products with price variations: {total_variations}")
        log.info(f"📊 Average price variation: {avg_variation:.1f}%")
        log.info(f"🔥 Maximum price variation: {max_variation:.1f}%")
        log.info(f"⚠️ Significant variations (≥10%): {len(significant_variations)}")
        log.info(f"🚨 Very significant variations (≥20%): {len(very_significant_variations)}")
        
        # Show distribution
        ranges = [
            (0, 5, "Minor (0-5%)"),
            (5, 10, "Moderate (5-10%)"),
            (10, 20, "Significant (10-20%)"),
            (20, 50, "Very significant (20-50%)"),
            (50, 100, "Extreme (>50%)")
        ]
        
        log.info(f"\n📊 Price variation distribution:")
        for min_val, max_val, label in ranges:
            count = len([v for v in price_variations if min_val <= v['variation_percent'] < max_val])
            if count > 0:
                log.info(f"   {label}: {count} products")
    
    # Save detailed results
    output_file = "price_variations_analysis.json"
    with open(output_file, 'w') as f:
        json.dump({
            'summary': {
                'total_products_analyzed': len(products_by_ean),
                'products_with_variations': len(price_variations),
                'average_variation_percent': avg_variation if price_variations else 0,
                'max_variation_percent': max_variation if price_variations else 0
            },
            'price_variations': price_variations
        }, f, indent=2, ensure_ascii=False)
    
    log.info(f"\n💾 Detailed analysis saved to {output_file}")
    
    return price_variations

if __name__ == "__main__":
    analyze_price_variations()
