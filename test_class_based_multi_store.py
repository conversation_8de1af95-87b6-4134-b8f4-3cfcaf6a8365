#!/usr/bin/env python3
"""
Test multiple stores using EXACT class structure from comprehensive_ean_test.py
"""

import requests
import json
import time
import logging
from datetime import datetime

# Set up logging like comprehensive test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

class IntermarheEANTester:
    """EXACT class structure from comprehensive_ean_test.py"""
    
    def __init__(self, store_id, city_code, store_name="Unknown"):
        self.store_id = store_id
        self.city_code = city_code
        self.store_name = store_name
        self.base_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}"
        
        # EXACT session and headers from comprehensive test
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
    
    def establish_store_context(self):
        """EXACT method from comprehensive_ean_test.py"""
        try:
            log.info(f"🏪 Establishing context for {self.store_id} ({self.store_name})")
            
            # Store page
            store_url = f"{self.base_url}/infos-pratiques"
            store_response = self.session.get(store_url, headers=self.headers, timeout=30)
            log.info(f"📍 Store page: {store_response.status_code}")
            
            if store_response.status_code != 200:
                return False
            
            time.sleep(1)
            
            # GET /accueil
            get_accueil_headers = self.headers.copy()
            get_accueil_headers['Referer'] = store_url
            get_accueil_response = self.session.get(f"{self.base_url}/accueil", headers=get_accueil_headers, timeout=30)
            log.info(f"🏠 GET Accueil: {get_accueil_response.status_code}")
            
            if get_accueil_response.status_code != 200:
                return False
            
            time.sleep(1)
            
            # POST to /accueil
            post_accueil_headers = self.headers.copy()
            post_accueil_headers.update({
                'Accept': 'text/x-component',
                'Content-Type': 'text/plain;charset=UTF-8',
                'next-action': '0021d05dbc1bd15b63b38f154a7cf55a9cc8fea62c',
                'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2Faccueil%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            })
            
            post_accueil_response = self.session.post(f"{self.base_url}/accueil", headers=post_accueil_headers, data='[]', timeout=30)
            log.info(f"🚀 POST Accueil: {post_accueil_response.status_code}")
            
            return post_accueil_response.status_code == 200
                
        except Exception as e:
            log.error(f"❌ Error establishing store context: {e}")
            return False
    
    def test_suggestion_api(self):
        """Test suggestion API - simplified version"""
        try:
            log.info("🔍 Testing suggestion API")
            
            api_headers = self.headers.copy()
            api_headers.update({
                'Accept': '*/*',
                'Content-Type': 'application/json',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-red-device': 'red_fo_desktop',
                'x-red-version': '3',
                'x-service-name': 'produits',
                'x-optional-oauth': 'true',
                'x-is-server': 'false'
            })
            
            suggestion_url = f"https://www.intermarche.com/api/service/produits/v1/pdvs/{self.store_id}/products/suggestion"
            suggestion_data = {
                "query": "lait",
                "category": "4653",
                "limit": 5
            }
            
            time.sleep(0.5)
            
            response = self.session.post(suggestion_url, headers=api_headers, json=suggestion_data, timeout=30)
            log.info(f"📊 Suggestion API: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                suggestions = result.get('suggestions', [])
                log.info(f"✅ Got {len(suggestions)} suggestions")
                
                # Extract EANs
                ean_codes = []
                for suggestion in suggestions:
                    if 'ean' in suggestion:
                        ean_codes.append(suggestion['ean'])
                
                return ean_codes
            else:
                log.warning(f"⚠️ Suggestion API failed: {response.status_code}")
                return []
                
        except Exception as e:
            log.error(f"❌ Error in suggestion API: {e}")
            return []
    
    def test_byeans_api(self, ean_codes):
        """Test byEans API - EXACT from comprehensive test"""
        if not ean_codes:
            return []
        
        try:
            log.info(f"🛒 Testing byEans API with {len(ean_codes)} EANs")
            
            api_headers = self.headers.copy()
            api_headers.update({
                'Accept': '*/*',
                'Content-Type': 'application/json',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-red-device': 'red_fo_desktop',
                'x-red-version': '3',
                'x-service-name': 'produits',
                'x-optional-oauth': 'true',
                'x-is-server': 'false'
            })
            
            byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{self.store_id}/products/byEans"
            byeans_data = {"eans": ean_codes}
            
            time.sleep(0.5)
            
            response = self.session.post(byeans_url, headers=api_headers, json=byeans_data, timeout=30)
            log.info(f"🛍️ ByEans API: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                products = result.get('products', [])
                log.info(f"✅ Got {len(products)} products")
                
                # Show sample products
                for i, product in enumerate(products[:2]):
                    name = product.get('libelle', 'Unknown')
                    price = product.get('prix', 'N/A')
                    stock = product.get('stock', {}).get('quantity', 'N/A')
                    log.info(f"   📦 {name} - €{price} (Stock: {stock})")
                
                return products
            else:
                log.warning(f"⚠️ ByEans API failed: {response.status_code}")
                return []
                
        except Exception as e:
            log.error(f"❌ Error in byEans API: {e}")
            return []
    
    def run_full_test(self):
        """Run complete test sequence"""
        log.info("=" * 60)
        log.info(f"🚀 Starting test for {self.store_name} ({self.store_id})")
        log.info("=" * 60)
        
        # Step 1: Establish context
        if not self.establish_store_context():
            log.error("❌ Failed to establish store context")
            return {
                'store_id': self.store_id,
                'store_name': self.store_name,
                'success': False,
                'error': 'Context establishment failed'
            }
        
        # Step 2: Test suggestion API
        ean_codes = self.test_suggestion_api()
        if not ean_codes:
            log.warning("⚠️ No EAN codes collected")
            return {
                'store_id': self.store_id,
                'store_name': self.store_name,
                'success': False,
                'error': 'No EAN codes found'
            }
        
        # Step 3: Test byEans API
        products = self.test_byeans_api(ean_codes)
        
        result = {
            'store_id': self.store_id,
            'store_name': self.store_name,
            'success': len(products) > 0,
            'eans_collected': len(ean_codes),
            'products_retrieved': len(products),
            'products': products[:5]  # Store first 5 products
        }
        
        if result['success']:
            log.info(f"✅ SUCCESS: {result['products_retrieved']} products retrieved")
        else:
            log.error("❌ FAILED: No products retrieved")
        
        return result

def main():
    """Test multiple stores using class-based approach"""
    
    # Test stores
    test_stores = [
        ('12118', 'marseille-13004', 'Super Marseille - KNOWN WORKING'),
        ('11691', 'arbent-01100', 'Arbent Store'),
        ('10525', 'bourg-en-bresse-01000', 'Bourg-en-Bresse Store'),
    ]
    
    all_results = []
    
    for i, (store_id, city_code, store_name) in enumerate(test_stores, 1):
        log.info(f"\n{'='*80}")
        log.info(f"TESTING STORE {i}/{len(test_stores)}")
        
        # Create tester instance
        tester = IntermarheEANTester(store_id, city_code, store_name)
        
        # Run test
        result = tester.run_full_test()
        all_results.append(result)
        
        # Delay between stores
        if i < len(test_stores):
            log.info(f"⏳ Waiting 20 seconds before next store...")
            time.sleep(20)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"class_based_multi_store_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    log.info(f"\n{'='*80}")
    log.info("FINAL SUMMARY")
    log.info(f"{'='*80}")
    
    successful_stores = [r for r in all_results if r['success']]
    failed_stores = [r for r in all_results if not r['success']]
    
    log.info(f"Total stores tested: {len(all_results)}")
    log.info(f"Successful stores: {len(successful_stores)}")
    log.info(f"Failed stores: {len(failed_stores)}")
    log.info(f"Success rate: {len(successful_stores)/len(all_results)*100:.1f}%")
    
    if successful_stores:
        log.info("\n✅ WORKING STORES:")
        for store in successful_stores:
            log.info(f"   - {store['store_id']} ({store['store_name']}): {store['products_retrieved']} products")
    
    if failed_stores:
        log.info("\n❌ FAILED STORES:")
        for store in failed_stores:
            log.info(f"   - {store['store_id']} ({store['store_name']}): {store.get('error', 'Unknown error')}")
    
    log.info(f"\nDetailed results saved to: {results_file}")

if __name__ == "__main__":
    main()
