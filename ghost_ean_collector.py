#!/usr/bin/env python3
"""
Ghost Browser-based EAN Collector for Intermarché
Advanced stealth scraper with comprehensive captcha bypass techniques
Uses multiple anti-detection methods and human-like behavior simulation
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium_stealth import stealth
import undetected_chromedriver as uc
import time
import json
import csv
import logging
import re
import random
import string
from datetime import datetime
import os
from bs4 import BeautifulSoup
import requests
from fake_useragent import UserAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ghost_ean_collector.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
log = logging.getLogger(__name__)

class GhostBrowserEANCollector:
    def __init__(self, headless=False):
        self.base_url = "https://www.intermarche.com"
        self.headless = headless
        self.driver = None
        self.wait = None
        self.stores = []
        self.ua = UserAgent()
        self.session_count = 0
        
        # Search terms for comprehensive EAN collection
        self.search_terms = [
            "lait", "pain", "oeufs", "fromage", "yaourt", "beurre", "jambon", "poulet",
            "boeuf", "porc", "poisson", "saumon", "thon", "crevettes", "pommes", "bananes",
            "oranges", "tomates", "carottes", "pommes de terre", "salade", "courgettes",
            "pâtes", "riz", "farine", "sucre", "huile", "vinaigre", "sel", "poivre",
            "café", "thé", "chocolat", "biscuits", "céréales", "confiture"
        ]
        
        self.collected_eans = set()
        
        # Proxy list (add your proxies here)
        self.proxies = [
            # "http://proxy1:port",
            # "http://proxy2:port",
            # Add working proxies here
        ]
        self.current_proxy_index = 0
        
    def get_random_user_agent(self):
        """Get a random user agent"""
        try:
            return self.ua.random
        except:
            # Fallback user agents
            agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            return random.choice(agents)
    
    def human_like_delay(self, min_delay=1, max_delay=3):
        """Add human-like random delays"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def random_mouse_movement(self):
        """Simulate random mouse movements"""
        try:
            actions = ActionChains(self.driver)
            # Random movements
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                actions.move_by_offset(x, y)
                actions.pause(random.uniform(0.1, 0.5))
            actions.perform()
        except Exception as e:
            log.debug(f"Mouse movement failed: {e}")
    
    def setup_driver(self):
        """Setup undetected Chrome driver with advanced stealth options"""
        log.info("Setting up Ghost Chrome driver...")

        try:
            # Use undetected-chromedriver for better stealth
            options = uc.ChromeOptions()

            # Basic stealth options
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1366,768")

            # Random user agent
            user_agent = self.get_random_user_agent()
            options.add_argument(f"--user-agent={user_agent}")
            log.info(f"Using User Agent: {user_agent}")

            # Proxy support
            if self.proxies and len(self.proxies) > 0:
                proxy = self.proxies[self.current_proxy_index % len(self.proxies)]
                options.add_argument(f"--proxy-server={proxy}")
                log.info(f"Using proxy: {proxy}")
                self.current_proxy_index += 1

            if self.headless:
                options.add_argument("--headless=new")

            # Create undetected Chrome instance with minimal options
            self.driver = uc.Chrome(options=options, version_main=None)

            # Apply selenium-stealth
            try:
                stealth(self.driver,
                        languages=["en-US", "en"],
                        vendor="Google Inc.",
                        platform="Win32",
                        webgl_vendor="Intel Inc.",
                        renderer="Intel Iris OpenGL Engine",
                        fix_hairline=True,
                        )
                log.info("Stealth mode applied successfully")
            except Exception as stealth_error:
                log.warning(f"Stealth application failed: {stealth_error}")
                # Continue without stealth if it fails
            
            # Additional JavaScript to hide automation
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
                window.chrome = {runtime: {}};
                Object.defineProperty(navigator, 'permissions', {get: () => ({query: () => Promise.resolve({state: 'granted'})})});
                
                // Override canvas fingerprinting
                const getContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(type) {
                    if (type === '2d') {
                        const context = getContext.call(this, type);
                        const getImageData = context.getImageData;
                        context.getImageData = function(x, y, w, h) {
                            const imageData = getImageData.call(this, x, y, w, h);
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                imageData.data[i] += Math.floor(Math.random() * 10) - 5;
                                imageData.data[i + 1] += Math.floor(Math.random() * 10) - 5;
                                imageData.data[i + 2] += Math.floor(Math.random() * 10) - 5;
                            }
                            return imageData;
                        };
                        return context;
                    }
                    return getContext.call(this, type);
                };
                
                // Override WebGL fingerprinting
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) {
                        return 'Intel Inc.';
                    }
                    if (parameter === 37446) {
                        return 'Intel Iris OpenGL Engine';
                    }
                    return getParameter.call(this, parameter);
                };
            """)
            
            self.wait = WebDriverWait(self.driver, 30)
            self.session_count += 1
            
            log.info(f"Ghost Chrome driver setup successful (Session #{self.session_count})")
            return True
            
        except Exception as e:
            log.error(f"Failed to setup Ghost Chrome driver: {e}")
            return False
    
    def save_debug_html(self, filename_suffix=""):
        """Save current page HTML for debugging"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_ghost_{timestamp}_{filename_suffix}.html"
            
            html_content = self.driver.page_source
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            log.info(f"Debug HTML saved: {filename}")
            return filename
        except Exception as e:
            log.error(f"Failed to save debug HTML: {e}")
            return None
    
    def detect_and_handle_captcha(self):
        """Detect and attempt to handle various captcha types"""
        log.info("Checking for captcha...")
        
        captcha_indicators = [
            "captcha", "recaptcha", "hcaptcha", "cloudflare", "challenge",
            "verification", "robot", "human", "security check"
        ]
        
        page_source = self.driver.page_source.lower()
        
        for indicator in captcha_indicators:
            if indicator in page_source:
                log.warning(f"Captcha detected: {indicator}")
                self.save_debug_html(f"captcha_{indicator}")
                
                # Try various bypass methods
                return self.bypass_captcha()
        
        return True
    
    def bypass_captcha(self):
        """Attempt various captcha bypass techniques"""
        log.info("Attempting captcha bypass...")
        
        # Method 1: Wait and retry
        log.info("Method 1: Waiting for captcha to resolve...")
        self.human_like_delay(10, 20)
        
        # Method 2: Random mouse movements
        log.info("Method 2: Simulating human behavior...")
        self.random_mouse_movement()
        self.human_like_delay(5, 10)
        
        # Method 3: Refresh page
        log.info("Method 3: Refreshing page...")
        self.driver.refresh()
        self.human_like_delay(5, 10)
        
        # Method 4: Change user agent and restart
        log.info("Method 4: Changing session...")
        return self.restart_session()
    
    def restart_session(self):
        """Restart browser session with new fingerprint"""
        log.info("Restarting session with new fingerprint...")
        
        try:
            if self.driver:
                self.driver.quit()
            
            # Wait before restarting
            self.human_like_delay(30, 60)
            
            # Setup new driver
            return self.setup_driver()
            
        except Exception as e:
            log.error(f"Failed to restart session: {e}")
            return False

    def load_stores(self):
        """Load store mappings from CSV"""
        try:
            with open('store_mappings_clean.csv', 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                self.stores = [(row[0], row[1]) for row in reader if len(row) >= 2]
            log.info(f"Loaded {len(self.stores)} stores")
            return True
        except Exception as e:
            log.error(f"Failed to load stores: {e}")
            return False

    def navigate_to_store(self, store_id, store_city, max_retries=3):
        """Navigate to a specific store with captcha handling"""
        log.info(f"Navigating to store {store_id} ({store_city})")

        for attempt in range(max_retries):
            try:
                # Go to store page
                store_url = f"{self.base_url}/magasins/{store_id}/{store_city}/infos-pratiques"
                log.info(f"Loading store page: {store_url} (Attempt {attempt + 1})")

                self.driver.get(store_url)

                # Human-like behavior
                self.human_like_delay(3, 8)
                self.random_mouse_movement()

                # Check for captcha
                if not self.detect_and_handle_captcha():
                    log.warning(f"Captcha handling failed on attempt {attempt + 1}")
                    continue

                # Save debug HTML
                self.save_debug_html(f"store_page_{store_id}_attempt_{attempt + 1}")

                # Look for "Faire mes courses" button with multiple strategies
                log.info("Looking for 'Faire mes courses' button...")

                shopping_button_selectors = [
                    "//button[@aria-label='Faire mes courses']",
                    "//button[contains(text(), 'Faire mes courses')]",
                    "//a[contains(text(), 'Faire mes courses')]",
                    "//button[contains(@class, 'stime_button--red') and contains(text(), 'Faire mes courses')]",
                    "//div[contains(text(), 'Faire mes courses')]/parent::button",
                    "//button[.//div[contains(text(), 'Faire mes courses')]]",
                    "//*[contains(text(), 'Faire mes courses')]"
                ]

                shopping_button = None
                for i, selector in enumerate(shopping_button_selectors):
                    try:
                        log.info(f"Trying selector {i+1}: {selector}")
                        shopping_button = WebDriverWait(self.driver, 15).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        log.info(f"Found shopping button with selector {i+1}")
                        break
                    except TimeoutException:
                        log.info(f"Selector {i+1} failed, trying next...")
                        continue

                if shopping_button:
                    log.info("Clicking 'Faire mes courses' button...")

                    # Scroll to button
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", shopping_button)
                    self.human_like_delay(1, 3)

                    # Human-like click
                    actions = ActionChains(self.driver)
                    actions.move_to_element(shopping_button)
                    actions.pause(random.uniform(0.5, 1.5))
                    actions.click()
                    actions.perform()

                    log.info("Button clicked, waiting for navigation...")
                    self.human_like_delay(5, 10)

                    # Check for captcha after click
                    if not self.detect_and_handle_captcha():
                        log.warning("Captcha detected after button click")
                        continue

                    # Save debug HTML after clicking
                    self.save_debug_html(f"after_courses_click_{store_id}_attempt_{attempt + 1}")

                    log.info("Successfully navigated to shopping section")
                    return True
                else:
                    log.warning("Could not find 'Faire mes courses' button")
                    # Try direct navigation to shopping page
                    shopping_url = f"{self.base_url}/accueil"
                    log.info(f"Trying direct navigation to: {shopping_url}")
                    self.driver.get(shopping_url)
                    self.human_like_delay(3, 8)

                    if self.detect_and_handle_captcha():
                        self.save_debug_html(f"direct_shopping_{store_id}_attempt_{attempt + 1}")
                        return True

            except Exception as e:
                log.error(f"Error navigating to store (attempt {attempt + 1}): {e}")
                self.save_debug_html(f"error_navigation_{store_id}_attempt_{attempt + 1}")

                if attempt < max_retries - 1:
                    log.info("Restarting session for next attempt...")
                    if not self.restart_session():
                        break
                    self.human_like_delay(10, 20)

        log.error(f"Failed to navigate to store {store_id} after {max_retries} attempts")
        return False

    def search_products(self, search_term, max_pages=2, max_retries=3):
        """Search for products and collect EANs with captcha handling"""
        log.info(f"Searching for products: '{search_term}'")
        collected_eans = set()

        for attempt in range(max_retries):
            try:
                # Wait for page to be ready
                self.human_like_delay(2, 5)

                # Check for captcha before search
                if not self.detect_and_handle_captcha():
                    log.warning(f"Captcha detected before search (attempt {attempt + 1})")
                    continue

                # Find search box with comprehensive selectors
                search_selectors = [
                    "//input[@id='search-input__input']",
                    "//input[@type='search']",
                    "//input[contains(@placeholder, 'recherche')]",
                    "//input[contains(@placeholder, 'Recherche')]",
                    "//input[contains(@placeholder, 'Rechercher')]",
                    "//input[contains(@class, 'search')]",
                    "//input[@name='q']",
                    "//input[@id='search']",
                    "//input[contains(@aria-label, 'recherche')]"
                ]

                search_box = None
                for i, selector in enumerate(search_selectors):
                    try:
                        log.info(f"Trying search selector {i+1}: {selector}")
                        search_box = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        log.info(f"Found search box with selector {i+1}")
                        break
                    except TimeoutException:
                        log.info(f"Search selector {i+1} failed, trying next...")
                        continue

                if not search_box:
                    log.error("Could not find search box")
                    self.save_debug_html(f"no_search_box_{search_term}_attempt_{attempt + 1}")

                    if attempt < max_retries - 1:
                        self.restart_session()
                        continue
                    return collected_eans

                # Human-like search interaction
                log.info(f"Entering search term: '{search_term}'")

                # Click on search box
                actions = ActionChains(self.driver)
                actions.move_to_element(search_box)
                actions.pause(random.uniform(0.5, 1.0))
                actions.click()
                actions.perform()

                self.human_like_delay(0.5, 1.5)

                # Clear and type with human-like typing
                search_box.clear()
                self.human_like_delay(0.5, 1.0)

                # Type character by character with random delays
                for char in search_term:
                    search_box.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.2))

                self.human_like_delay(0.5, 1.5)
                search_box.send_keys(Keys.RETURN)

                log.info(f"Submitted search for: {search_term}")
                self.human_like_delay(5, 10)

                # Check for captcha after search
                if not self.detect_and_handle_captcha():
                    log.warning("Captcha detected after search")
                    continue

                # Save search results page
                self.save_debug_html(f"search_results_{search_term}_attempt_{attempt + 1}")

                # Process search results pages
                for page in range(1, max_pages + 1):
                    log.info(f"Processing search results page {page}")

                    # Extract EANs from current page
                    page_eans = self.extract_eans_from_page(search_term, page)
                    collected_eans.update(page_eans)

                    log.info(f"Found {len(page_eans)} EANs on page {page}")

                    # Try to go to next page
                    if page < max_pages:
                        if not self.go_to_next_page():
                            log.info("No more pages available")
                            break
                        self.human_like_delay(3, 8)

                        # Check for captcha on new page
                        if not self.detect_and_handle_captcha():
                            log.warning("Captcha detected on pagination")
                            break

                # Success - break retry loop
                break

            except Exception as e:
                log.error(f"Error during search (attempt {attempt + 1}): {e}")
                self.save_debug_html(f"search_error_{search_term}_attempt_{attempt + 1}")

                if attempt < max_retries - 1:
                    log.info("Restarting session for next search attempt...")
                    if not self.restart_session():
                        break
                    self.human_like_delay(10, 20)

        log.info(f"Total EANs collected for '{search_term}': {len(collected_eans)}")
        return collected_eans

    def extract_eans_from_page(self, search_term, page_num):
        """Extract EAN codes from current page"""
        eans = set()

        try:
            # Get page source and parse with BeautifulSoup
            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')

            # Multiple strategies to find EANs
            ean_patterns = [
                r'\b\d{13}\b',  # 13-digit EAN
                r'\b\d{8}\b',   # 8-digit EAN
                r'ean["\s:]*(\d{8,13})',  # EAN with label
                r'gtin["\s:]*(\d{8,13})', # GTIN codes
                r'barcode["\s:]*(\d{8,13})', # Barcode
                r'code["\s:]*(\d{8,13})'  # Generic code
            ]

            # Search in various HTML elements
            search_areas = [
                soup.find_all('div', class_=re.compile(r'product|item|card')),
                soup.find_all('span', class_=re.compile(r'ean|code|gtin')),
                soup.find_all('data-ean'),
                soup.find_all('data-gtin'),
                soup.find_all(attrs={'data-ean': True}),
                soup.find_all(attrs={'data-gtin': True}),
                soup.find_all(attrs={'data-product-id': True})
            ]

            # Extract from text content
            for pattern in ean_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0] if match else ""
                    if self.validate_ean(match):
                        eans.add(match)

            # Extract from data attributes
            for area_list in search_areas:
                if area_list:
                    for element in area_list:
                        if hasattr(element, 'attrs'):
                            for attr_name, attr_value in element.attrs.items():
                                if any(keyword in attr_name.lower() for keyword in ['ean', 'gtin', 'code', 'product']):
                                    if isinstance(attr_value, str) and self.validate_ean(attr_value):
                                        eans.add(attr_value)

            log.info(f"Extracted {len(eans)} valid EANs from page {page_num}")

        except Exception as e:
            log.error(f"Error extracting EANs from page: {e}")

        return eans

    def validate_ean(self, ean_code):
        """Validate EAN code with checksum"""
        if not ean_code or not isinstance(ean_code, str):
            return False

        # Remove any non-digit characters
        ean_code = re.sub(r'\D', '', ean_code)

        # Check length (8 or 13 digits)
        if len(ean_code) not in [8, 13]:
            return False

        # Validate checksum for 13-digit EAN
        if len(ean_code) == 13:
            try:
                digits = [int(d) for d in ean_code]
                checksum = sum(digits[i] if i % 2 == 0 else digits[i] * 3 for i in range(12)) % 10
                return (10 - checksum) % 10 == digits[12]
            except:
                return False

        # For 8-digit EAN, just check if all digits
        return True

    def go_to_next_page(self):
        """Navigate to next page of search results"""
        try:
            next_selectors = [
                "//a[contains(text(), 'Suivant')]",
                "//button[contains(text(), 'Suivant')]",
                "//a[contains(@class, 'next')]",
                "//button[contains(@class, 'next')]",
                "//a[@aria-label='Next']",
                "//button[@aria-label='Next']",
                "//a[contains(@class, 'pagination')]//following-sibling::a",
                "//button[contains(@class, 'pagination')]//following-sibling::button"
            ]

            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.XPATH, selector)
                    if next_button.is_enabled():
                        # Human-like click
                        actions = ActionChains(self.driver)
                        actions.move_to_element(next_button)
                        actions.pause(random.uniform(0.5, 1.5))
                        actions.click()
                        actions.perform()

                        log.info("Navigated to next page")
                        return True
                except:
                    continue

            log.info("No next page button found")
            return False

        except Exception as e:
            log.error(f"Error navigating to next page: {e}")
            return False

    def save_eans_to_file(self, eans, filename=None):
        """Save collected EANs to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ghost_collected_eans_{timestamp}.csv"

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['EAN', 'Timestamp'])

                timestamp = datetime.now().isoformat()
                for ean in sorted(eans):
                    writer.writerow([ean, timestamp])

            log.info(f"Saved {len(eans)} EANs to {filename}")
            return filename

        except Exception as e:
            log.error(f"Error saving EANs to file: {e}")
            return None

    def close_driver(self):
        """Close the browser driver"""
        try:
            if self.driver:
                self.driver.quit()
                log.info("Browser driver closed")
        except Exception as e:
            log.error(f"Error closing driver: {e}")

def main():
    """Main execution function"""
    log.info("="*60)
    log.info("GHOST EAN COLLECTOR - ADVANCED CAPTCHA BYPASS")
    log.info("="*60)

    collector = GhostBrowserEANCollector(headless=False)  # Set to True for headless mode

    try:
        # Setup driver
        if not collector.setup_driver():
            log.error("Failed to setup driver")
            return

        # Load stores
        if not collector.load_stores():
            log.error("Failed to load stores")
            return

        stores = collector.stores
        log.info(f"Loaded {len(stores)} stores")

        # Test with first few stores
        test_stores = stores[:3]

        for store_id, store_city in test_stores:
            log.info(f"Testing with store: {store_id} ({store_city})")

            try:
                if collector.navigate_to_store(store_id, store_city):
                    log.info("Store navigation successful, starting EAN collection...")

                    all_eans = set()
                    test_terms = collector.search_terms[:2]  # Test with first 2 search terms

                    for search_term in test_terms:
                        log.info(f"\n{'='*50}")
                        log.info(f"SEARCHING FOR: {search_term}")
                        log.info(f"{'='*50}")

                        eans = collector.search_products(search_term, max_pages=1)
                        all_eans.update(eans)

                        log.info(f"Collected {len(eans)} EANs for '{search_term}'")
                        log.info(f"Total unique EANs: {len(all_eans)}")

                        # Save progress
                        if all_eans:
                            collector.save_eans_to_file(all_eans)

                        # Delay between searches
                        collector.human_like_delay(5, 15)

                    # Final results
                    log.info(f"\n{'='*50}")
                    log.info(f"COLLECTION COMPLETE")
                    log.info(f"{'='*50}")
                    log.info(f"Total unique EANs collected: {len(all_eans)}")

                    if all_eans:
                        final_file = collector.save_eans_to_file(all_eans, "final_ghost_ean_collection.csv")
                        log.info(f"Final collection saved to: {final_file}")

                        # Show sample EANs
                        sample_eans = list(all_eans)[:10]
                        log.info(f"Sample EANs: {sample_eans}")
                        break  # Success with this store
                    else:
                        log.warning("No EANs collected - check debug HTML files")
                        log.info("Continuing to next store...")
                else:
                    log.warning(f"Failed to navigate to store {store_id}, trying next store...")
                    continue

            except Exception as e:
                log.error(f"Error processing store {store_id}: {e}")
                collector.save_debug_html(f"error_store_{store_id}")
                continue

        log.info("Ghost EAN collection completed!")

    except KeyboardInterrupt:
        log.info("Collection interrupted by user")
    except Exception as e:
        log.error(f"Unexpected error: {e}")
    finally:
        collector.close_driver()

if __name__ == "__main__":
    main()
