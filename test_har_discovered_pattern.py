#!/usr/bin/env python3
"""
Test the EXACT pattern discovered from HAR analysis - NO accueil, direct API calls
"""

import requests
import json
import time
from datetime import datetime

def test_har_pattern(store_id, city_code, store_name="Unknown"):
    """Test using EXACT pattern from HAR analysis - NO accueil context"""
    
    print(f"\n🔍 TESTING HAR PATTERN FOR STORE {store_id} ({city_code}) - {store_name}")
    print("=" * 60)
    
    session = requests.Session()
    
    # Headers from working HAR sequence
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    results = {
        'store_id': store_id,
        'city_code': city_code,
        'store_name': store_name,
        'steps': {}
    }
    
    try:
        # Step 1: Store info page (EXACT from HAR)
        print("📍 Step 1: Accessing store info page...")
        store_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/infos-pratiques"
        print(f"   URL: {store_url}")
        
        # Set referer like in HAR
        store_headers = headers.copy()
        store_headers['Referer'] = 'https://www.intermarche.com/enseigne/magazine/tous-les-magasins'
        
        response = session.get(store_url, headers=store_headers, timeout=30)
        print(f"   Status: {response.status_code}")
        
        results['steps']['store_page'] = {
            'url': store_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code != 200:
            print(f"   ❌ FAILED: Store page returned {response.status_code}")
            return results
        
        print("   ✅ Store page OK")
        
        # Step 2: Get PDV info (EXACT from HAR)
        print("📊 Step 2: Getting PDV info...")
        pdv_headers = headers.copy()
        pdv_headers.update({
            'Accept': '*/*',
            'Referer': store_url,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        pdv_url = f"https://www.intermarche.com/api/pdv-info?pdvRef={store_id}"
        print(f"   URL: {pdv_url}")
        
        response = session.get(pdv_url, headers=pdv_headers, timeout=30)
        print(f"   Status: {response.status_code}")
        
        results['steps']['pdv_info'] = {
            'url': pdv_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code != 200:
            print(f"   ❌ FAILED: PDV info returned {response.status_code}")
            return results
        
        print("   ✅ PDV info OK")
        
        # Step 3: Test suggestion API (EXACT from HAR)
        print("🔍 Step 3: Testing suggestion API...")
        
        # Generate session ID like in HAR
        session_id = f"b71e80c4-dcbf-4f04-93ff-{int(time.time())}"
        
        suggestion_headers = headers.copy()
        suggestion_headers.update({
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'Origin': 'https://www.intermarche.com',
            'Referer': 'https://www.intermarche.com/accueil',  # From HAR
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'x-itm-session-id': session_id
        })
        
        suggestion_url = f"https://www.intermarche.com/api/service/produits/v1/pdvs/{store_id}/products/suggestion"
        suggestion_data = {
            "productNumber": 16,
            "criterias": [{"type": "boutique", "value": "4653"}]
        }
        
        print(f"   URL: {suggestion_url}")
        print(f"   Data: {suggestion_data}")
        print(f"   Session ID: {session_id}")
        
        response = session.post(suggestion_url, headers=suggestion_headers, json=suggestion_data, timeout=30)
        print(f"   Status: {response.status_code}")
        
        results['steps']['suggestion_api'] = {
            'url': suggestion_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code == 200:
            result = response.json()
            suggestions = result.get('suggestions', [])
            print(f"   ✅ Suggestion API OK - Got {len(suggestions)} suggestions")
            results['steps']['suggestion_api']['suggestions_count'] = len(suggestions)
            
            # Show sample suggestions
            for i, suggestion in enumerate(suggestions[:3]):
                name = suggestion.get('name', 'Unknown')
                ean = suggestion.get('ean', 'No EAN')
                print(f"      📦 {name} (EAN: {ean})")
        else:
            print(f"   ❌ Suggestion API failed: {response.status_code}")
            return results
        
        # Step 4: Test byKeywordAndCategory API (EXACT from HAR)
        print("🛒 Step 4: Testing byKeywordAndCategory API...")
        
        search_headers = headers.copy()
        search_headers.update({
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'Origin': 'https://www.intermarche.com',
            'Referer': 'https://www.intermarche.com/recherche/lait',  # From HAR
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'x-itm-session-id': session_id
        })
        
        search_url = f"https://www.intermarche.com/api/service/produits/v4/pdvs/{store_id}/products/byKeywordAndCategory"
        search_data = {
            "keyword": "lait",
            "page": 1,
            "size": 40,
            "filtres": [],
            "tri": "pertinence",
            "ordreTri": None,
            "catalog": ["PDV"]
        }
        
        print(f"   URL: {search_url}")
        print(f"   Data: {search_data}")
        
        response = session.post(search_url, headers=search_headers, json=search_data, timeout=30)
        print(f"   Status: {response.status_code}")
        
        results['steps']['search_api'] = {
            'url': search_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code == 200:
            result = response.json()
            products = result.get('products', [])
            print(f"   ✅ Search API OK - Got {len(products)} products")
            results['steps']['search_api']['products_count'] = len(products)
            
            # Show sample products
            for i, product in enumerate(products[:3]):
                name = product.get('libelle', 'Unknown')
                price = product.get('prix', 'N/A')
                ean = product.get('produitEan13', 'No EAN')
                print(f"      📦 {name} - €{price} (EAN: {ean})")
        else:
            print(f"   ❌ Search API failed: {response.status_code}")
        
        return results
        
    except Exception as e:
        print(f"   💥 ERROR: {str(e)}")
        results['error'] = str(e)
        return results

def main():
    """Test multiple stores using HAR-discovered pattern"""
    
    print("🔍 TESTING HAR-DISCOVERED PATTERN (NO ACCUEIL CONTEXT)")
    print("=" * 80)
    print("Key insight: Working store 12118 NEVER uses /accueil endpoints!")
    print("It goes directly from store page to API calls.")
    print("=" * 80)
    
    # Test stores including our known working one + others
    test_stores = [
        ('12118', 'marseille-13004', 'Super Marseille - KNOWN WORKING'),
        ('11691', 'arbent-01100', 'Arbent Store'),
        ('10525', 'bourg-en-bresse-01000', 'Bourg-en-Bresse Store'),
    ]
    
    all_results = []
    
    for i, (store_id, city_code, store_name) in enumerate(test_stores, 1):
        print(f"\n{'='*80}")
        print(f"TESTING STORE {i}/{len(test_stores)}")
        
        result = test_har_pattern(store_id, city_code, store_name)
        all_results.append(result)
        
        # Delay between stores
        if i < len(test_stores):
            print(f"\n⏳ Waiting 10 seconds before next store...")
            time.sleep(10)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"har_pattern_test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n{'='*80}")
    print("SUMMARY OF HAR PATTERN TESTING")
    print(f"{'='*80}")
    
    successful_stores = []
    failed_stores = []
    
    for result in all_results:
        store_id = result['store_id']
        city_code = result['city_code']
        store_name = result['store_name']
        
        print(f"\n🏪 {store_name} ({store_id})")
        
        steps = result.get('steps', {})
        all_success = True
        for step_name, step_data in steps.items():
            status = "✅" if step_data.get('success', False) else "❌"
            print(f"   {status} {step_name}: {step_data.get('status', 'N/A')}")
            if not step_data.get('success', False):
                all_success = False
        
        if all_success:
            successful_stores.append(result)
        else:
            failed_stores.append(result)
    
    print(f"\n{'='*40}")
    print(f"FINAL RESULTS:")
    print(f"Total stores tested: {len(all_results)}")
    print(f"Successful stores: {len(successful_stores)}")
    print(f"Failed stores: {len(failed_stores)}")
    print(f"Success rate: {len(successful_stores)/len(all_results)*100:.1f}%")
    
    if successful_stores:
        print(f"\n✅ WORKING STORES:")
        for store in successful_stores:
            print(f"   - {store['store_id']} ({store['store_name']})")
    
    print(f"\nDetailed results saved to: {results_file}")

if __name__ == "__main__":
    main()
