#!/usr/bin/env python3
"""
Efficient multi-store EAN testing with minimal context establishment
"""

import requests
import json
import time
from datetime import datetime

def quick_context_establishment(session, store_id, city_code):
    """Minimal context establishment for API access"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        # Just access the store page to establish basic context
        store_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/infos-pratiques"
        response = session.get(store_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            return True, "Context established"
        else:
            return False, f"Failed: {response.status_code}"
            
    except Exception as e:
        return False, f"Error: {str(e)}"

def test_ean_api_with_context(session, store_id, ean_codes):
    """Test EAN API with established context"""
    
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'x-red-version': '3',
        'x-itm-device-fp': 'desktop',
        'x-itm-session-id': f'session_{int(time.time())}',
        'x-custom-hash': f'{store_id}-disconnected-desktop',
        'Origin': 'https://www.intermarche.com',
        'Referer': f'https://www.intermarche.com/magasins/{store_id}/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    try:
        # Test byEans API
        byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{store_id}/products/byEans"
        byeans_data = {"eans": ean_codes}
        
        response = session.post(byeans_url, headers=api_headers, json=byeans_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            products = result.get('products', [])
            return True, f"Success: {len(products)} products", products
        else:
            return False, f"API failed: {response.status_code}", []
            
    except Exception as e:
        return False, f"Error: {str(e)}", []

def main():
    """Test EAN codes efficiently across multiple stores"""
    
    # Load store mappings
    try:
        with open('store_mappings_clean.csv', 'r', encoding='utf-8') as f:
            store_mappings = []
            for line in f:
                line = line.strip()
                if line and ',' in line:
                    store_id, city_code = line.split(',', 1)
                    store_mappings.append((store_id, city_code))
    except FileNotFoundError:
        print("Error: store_mappings_clean.csv not found.")
        return
    
    print(f"Loaded {len(store_mappings)} store mappings")
    
    # Use EAN codes from our successful comprehensive test
    test_eans = [
        "3017620429484",  # Nutella
        "8715700421957",  # Product 2
        "3033491432774",  # Product 3
        "3250390822929",  # Product 4
        "3021762412464"   # Product 5
    ]
    
    print(f"Testing {len(test_eans)} EAN codes across stores")
    
    # Test a diverse sample of stores
    # Include known working store + sample from different regions
    test_stores = [
        ('12118', 'marseille-13004'),  # Known working (Marseille)
        ('11691', 'arbent-01100'),     # Ain region
        ('10525', 'bourg-en-bresse-01000'),  # Ain region
        ('10946', 'gex-01170'),        # Ain region
        ('06909', 'oyonnax-01100'),    # Ain region
    ]
    
    results = {
        'successful_stores': [],
        'failed_stores': [],
        'store_details': {},
        'pricing_comparison': {}
    }
    
    for i, (store_id, city_code) in enumerate(test_stores, 1):
        print(f"\n=== Testing Store {i}/{len(test_stores)}: {store_id} ({city_code}) ===")
        
        # Create fresh session for each store
        session = requests.Session()
        
        # Establish context
        context_success, context_msg = quick_context_establishment(session, store_id, city_code)
        print(f"Context: {context_msg}")
        
        if not context_success:
            results['failed_stores'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': context_msg
            })
            continue
        
        # Test EAN API
        api_success, api_msg, products = test_ean_api_with_context(session, store_id, test_eans)
        print(f"API: {api_msg}")
        
        if api_success and products:
            results['successful_stores'].append({
                'store_id': store_id,
                'city_code': city_code,
                'products_found': len(products)
            })
            
            # Store detailed results
            results['store_details'][store_id] = {
                'city_code': city_code,
                'products_found': len(products),
                'products': []
            }
            
            # Process products and collect pricing data
            for product in products:
                product_info = {
                    'name': product.get('libelle', 'Unknown'),
                    'price': product.get('prix', 'N/A'),
                    'stock_quantity': product.get('stock', {}).get('quantity', 'N/A'),
                    'ean': product.get('produitEan13', 'N/A'),
                    'brand': product.get('marque', 'N/A')
                }
                results['store_details'][store_id]['products'].append(product_info)
                
                # Collect pricing data for comparison
                ean = product_info['ean']
                if ean not in results['pricing_comparison']:
                    results['pricing_comparison'][ean] = {
                        'product_name': product_info['name'],
                        'stores': {}
                    }
                
                results['pricing_comparison'][ean]['stores'][store_id] = {
                    'price': product_info['price'],
                    'stock': product_info['stock_quantity'],
                    'city': city_code
                }
                
                # Display product info
                price_str = f"€{product_info['price']}" if product_info['price'] != 'N/A' else 'No price'
                stock_str = f"Stock: {product_info['stock_quantity']}" if product_info['stock_quantity'] != 'N/A' else 'No stock'
                print(f"  📦 {product_info['name']} - {price_str} ({stock_str})")
        else:
            results['failed_stores'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': api_msg
            })
        
        # Delay between stores
        time.sleep(3)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"efficient_multi_store_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total stores tested: {len(test_stores)}")
    print(f"Successful stores: {len(results['successful_stores'])}")
    print(f"Failed stores: {len(results['failed_stores'])}")
    print(f"Success rate: {len(results['successful_stores'])/len(test_stores)*100:.1f}%")
    print(f"\nResults saved to: {results_file}")
    
    if results['successful_stores']:
        print(f"\nWorking stores:")
        for store in results['successful_stores']:
            print(f"  - {store['store_id']} ({store['city_code']}): {store['products_found']} products")
    
    # Show pricing comparison if we have multiple working stores
    if len(results['successful_stores']) > 1:
        print(f"\n=== PRICING COMPARISON ===")
        for ean, data in results['pricing_comparison'].items():
            if len(data['stores']) > 1:  # Only show products found in multiple stores
                print(f"\n{data['product_name']} (EAN: {ean}):")
                for store_id, store_data in data['stores'].items():
                    price_str = f"€{store_data['price']}" if store_data['price'] != 'N/A' else 'No price'
                    print(f"  Store {store_id} ({store_data['city']}): {price_str}")

if __name__ == "__main__":
    main()
