#!/usr/bin/env python3
"""
Debug why store 12118 works but others don't - test exact same sequence
"""

import requests
import json
import time
from datetime import datetime

def test_store_sequence(store_id, city_code, store_name="Unknown"):
    """Test the exact same sequence that works for 12118 on other stores"""
    
    print(f"\n🔍 DEBUGGING STORE {store_id} ({city_code}) - {store_name}")
    print("=" * 60)
    
    session = requests.Session()
    
    # Exact headers from working comprehensive test
    store_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    results = {
        'store_id': store_id,
        'city_code': city_code,
        'store_name': store_name,
        'steps': {}
    }
    
    try:
        # Step 1: Store page
        print("📍 Step 1: Accessing store page...")
        store_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/infos-pratiques"
        print(f"   URL: {store_url}")
        
        response = session.get(store_url, headers=store_headers, timeout=15)
        print(f"   Status: {response.status_code}")
        
        results['steps']['store_page'] = {
            'url': store_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code != 200:
            print(f"   ❌ FAILED: Store page returned {response.status_code}")
            return results
        
        print("   ✅ Store page OK")
        
        # Step 2: GET accueil
        print("🏠 Step 2: GET accueil...")
        accueil_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/accueil"
        print(f"   URL: {accueil_url}")
        
        response = session.get(accueil_url, headers=store_headers, timeout=15)
        print(f"   Status: {response.status_code}")
        
        results['steps']['accueil_get'] = {
            'url': accueil_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code != 200:
            print(f"   ❌ FAILED: Accueil GET returned {response.status_code}")
            return results
        
        print("   ✅ Accueil GET OK")
        
        # Step 3: POST accueil
        print("🚀 Step 3: POST accueil...")
        post_headers = store_headers.copy()
        post_headers.update({
            'Accept': 'text/x-component',
            'Content-Type': 'text/plain;charset=UTF-8',
            'Next-Action': '6b0b5b5b8b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b',
            'Next-Router-State-Tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22magasins%22%2C%7B%22children%22%3A%5B%22%5Bslug%5D%22%2C%7B%22children%22%3A%5B%22%5Bslug%5D%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%7D%5D%7D%5D%7D%5D%7D%5D%7D%5D',
            'Referer': accueil_url,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        post_data = f'["{store_id}"]'
        print(f"   POST data: {post_data}")
        
        response = session.post(accueil_url, headers=post_headers, data=post_data, timeout=15)
        print(f"   Status: {response.status_code}")
        
        results['steps']['accueil_post'] = {
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code != 200:
            print(f"   ❌ FAILED: Accueil POST returned {response.status_code}")
            return results
        
        print("   ✅ Accueil POST OK")
        
        # Step 4: Test suggestion API
        print("🔍 Step 4: Testing suggestion API...")
        api_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'x-red-version': '3',
            'x-itm-device-fp': 'desktop',
            'x-itm-session-id': f'session_{int(time.time())}',
            'x-custom-hash': f'{store_id}-disconnected-desktop',
            'Origin': 'https://www.intermarche.com',
            'Referer': f'https://www.intermarche.com/magasins/{store_id}/',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        
        suggestion_url = f"https://www.intermarche.com/api/service/produits/v1/pdvs/{store_id}/products/suggestion"
        suggestion_data = {
            "query": "lait",
            "category": "4653",
            "limit": 5
        }
        
        print(f"   URL: {suggestion_url}")
        print(f"   Data: {suggestion_data}")
        
        response = session.post(suggestion_url, headers=api_headers, json=suggestion_data, timeout=15)
        print(f"   Status: {response.status_code}")
        
        results['steps']['suggestion_api'] = {
            'url': suggestion_url,
            'status': response.status_code,
            'success': response.status_code == 200
        }
        
        if response.status_code == 200:
            result = response.json()
            suggestions = result.get('suggestions', [])
            print(f"   ✅ Suggestion API OK - Got {len(suggestions)} suggestions")
            results['steps']['suggestion_api']['suggestions_count'] = len(suggestions)
            
            if suggestions:
                # Step 5: Test byEans API
                print("🛒 Step 5: Testing byEans API...")
                ean_codes = []
                for suggestion in suggestions[:3]:
                    if 'ean' in suggestion:
                        ean_codes.append(suggestion['ean'])
                
                if ean_codes:
                    byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{store_id}/products/byEans"
                    byeans_data = {"eans": ean_codes}
                    
                    print(f"   URL: {byeans_url}")
                    print(f"   EANs: {ean_codes}")
                    
                    response = session.post(byeans_url, headers=api_headers, json=byeans_data, timeout=15)
                    print(f"   Status: {response.status_code}")
                    
                    results['steps']['byeans_api'] = {
                        'url': byeans_url,
                        'status': response.status_code,
                        'success': response.status_code == 200
                    }
                    
                    if response.status_code == 200:
                        result = response.json()
                        products = result.get('products', [])
                        print(f"   ✅ ByEans API OK - Got {len(products)} products")
                        results['steps']['byeans_api']['products_count'] = len(products)
                        
                        # Show sample products
                        for i, product in enumerate(products[:2]):
                            name = product.get('libelle', 'Unknown')
                            price = product.get('prix', 'N/A')
                            print(f"      📦 {name} - €{price}")
                    else:
                        print(f"   ❌ ByEans API failed: {response.status_code}")
                else:
                    print("   ⚠️ No EAN codes found in suggestions")
            else:
                print("   ⚠️ No suggestions returned")
        else:
            print(f"   ❌ Suggestion API failed: {response.status_code}")
        
        return results
        
    except Exception as e:
        print(f"   💥 ERROR: {str(e)}")
        results['error'] = str(e)
        return results

def main():
    """Debug different stores to understand why some work and others don't"""
    
    # Load store mappings
    try:
        with open('store_mappings_clean.csv', 'r', encoding='utf-8') as f:
            store_mappings = []
            for line in f:
                line = line.strip()
                if line and ',' in line:
                    store_id, city_code = line.split(',', 1)
                    store_mappings.append((store_id, city_code))
    except FileNotFoundError:
        print("Error: store_mappings_clean.csv not found.")
        return
    
    print(f"Loaded {len(store_mappings)} store mappings")
    
    # Test stores: our known working one + a few others
    test_stores = [
        ('12118', 'marseille-13004', 'Super Marseille - KNOWN WORKING'),
        ('11691', 'arbent-01100', 'Arbent Store'),
        ('10525', 'bourg-en-bresse-01000', 'Bourg-en-Bresse Store'),
    ]
    
    all_results = []
    
    for i, (store_id, city_code, store_name) in enumerate(test_stores, 1):
        print(f"\n{'='*80}")
        print(f"TESTING STORE {i}/{len(test_stores)}")
        
        result = test_store_sequence(store_id, city_code, store_name)
        all_results.append(result)
        
        # Long delay between stores to avoid rate limiting
        if i < len(test_stores):
            print(f"\n⏳ Waiting 10 seconds before next store...")
            time.sleep(10)
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"store_debug_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n{'='*80}")
    print("SUMMARY OF RESULTS")
    print(f"{'='*80}")
    
    for result in all_results:
        store_id = result['store_id']
        city_code = result['city_code']
        store_name = result['store_name']
        
        print(f"\n🏪 {store_name} ({store_id})")
        
        steps = result.get('steps', {})
        for step_name, step_data in steps.items():
            status = "✅" if step_data.get('success', False) else "❌"
            print(f"   {status} {step_name}: {step_data.get('status', 'N/A')}")
    
    print(f"\nDetailed results saved to: {results_file}")

if __name__ == "__main__":
    main()
