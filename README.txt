================================================================================
                    INTERMARCHÉ PRICE COMPARISON SCRAPER PROJECT
================================================================================

This project provides a comprehensive solution for scraping product data and 
prices from Intermarché stores across France, enabling price comparison across 
2,086+ stores nationwide.

================================================================================
                                PROJECT OVERVIEW
================================================================================

The system consists of two main components:
1. BROWSER-BASED SCRAPER: Collects EAN codes from product searches
2. API-BASED VALIDATOR: Tests EANs across multiple stores for price comparison

Key Features:
- Scrapes 1,000+ EAN codes per search term
- Tests products across 2,086 Intermarché stores in France
- Detects price variations up to 25% for the same products
- Bypasses anti-bot protection using Camoufox browser
- Provides real-time pricing and stock information

================================================================================
                                    FILES GUIDE
================================================================================

📁 CORE SCRAPING FILES:
-----------------------

🔧 intermarche-products-scraper.py
   PURPOSE: Main browser-based scraper for collecting EAN codes
   FUNCTION: 
   - Uses Camoufox browser to bypass anti-bot detection
   - Navigates to specific Intermarché store pages
   - Searches for products (e.g., "lait", "pain", "beurre")
   - Handles pagination and lazy loading
   - Extracts EAN codes from product listings
   - Saves results to JSON files
   
   USAGE: python intermarche-products-scraper.py
   OUTPUT: intermarche_eans.json (contains 1,068+ EAN codes)

🔧 comprehensive_ean_test.py
   PURPOSE: API-based EAN validator and multi-store price tester
   FUNCTION:
   - Establishes proper session context for each store
   - Tests EAN codes using Intermarché's internal API
   - Retrieves detailed product data (prices, stock, descriptions)
   - Supports single store or multi-store testing
   - Handles batch processing (20 EANs per API call)
   
   USAGE OPTIONS:
   - python comprehensive_ean_test.py                    # Single store test
   - python comprehensive_ean_test.py multi             # Multi-store test  
   - python comprehensive_ean_test.py scraped           # Test scraped EANs
   - python comprehensive_ean_test.py ean <EAN_CODE>    # Test specific EAN

📁 ANALYSIS FILES:
------------------

🔧 analyze_price_variations.py
   PURPOSE: Analyzes price differences for same products across stores
   FUNCTION:
   - Processes multi-store test results
   - Identifies products available in multiple stores
   - Calculates price variations and statistics
   - Generates detailed price comparison reports
   
   USAGE: python analyze_price_variations.py
   INPUT: scraped_eans_multistore_results_*.json
   OUTPUT: price_variations_analysis.json

📁 DATA FILES:
--------------

📄 store_mappings_clean.csv
   PURPOSE: Complete database of all Intermarché stores in France
   FORMAT: store_id,city_code
   CONTENT: 2,086 stores with unique IDs and location codes
   EXAMPLE: 11621,villeneuve-loubet-06270
   
   USAGE: Referenced by scripts to select stores for testing

📁 html_logs/
   PURPOSE: Debug folder for storing HTML snapshots during scraping
   CONTENT: Empty (logs are created during scraping for debugging)
   USAGE: Helps troubleshoot scraping issues by saving page states

================================================================================
                                HOW THE PROJECT WORKS
================================================================================

🔄 COMPLETE WORKFLOW:
---------------------

PHASE 1: EAN COLLECTION
1. Run intermarche-products-scraper.py
2. Script opens Camoufox browser (anti-detection)
3. Navigates to Villeneuve-Loubet store (store ID: 11621)
4. Clicks "Faire mes courses" to establish shopping context
5. Searches for products (e.g., "lait")
6. Scrolls through all pages with lazy loading
7. Extracts EAN codes from each product
8. Saves 1,068+ EANs to intermarche_eans.json

PHASE 2: MULTI-STORE VALIDATION
1. Run comprehensive_ean_test.py scraped
2. Loads EANs from intermarche_eans.json
3. Selects random stores from store_mappings_clean.csv
4. For each store:
   - Establishes session context via store page
   - Makes API calls with batches of 20 EANs
   - Retrieves product data (price, stock, description)
5. Saves results with store-specific pricing

PHASE 3: PRICE ANALYSIS
1. Run analyze_price_variations.py
2. Compares same products across different stores
3. Identifies price variations (up to 25% difference found)
4. Generates statistical analysis and reports

🔧 TECHNICAL ARCHITECTURE:
--------------------------

BROWSER SCRAPING (Camoufox):
- Bypasses Cloudflare and anti-bot protection
- Handles JavaScript-heavy pages with lazy loading
- Maintains proper session cookies and headers
- Simulates real user behavior

API INTEGRATION:
- Uses Intermarché's internal product API
- Endpoint: /api/service/produits/v3/stores/{store_id}/products/byEans
- Requires proper session establishment per store
- Handles batch requests for efficiency

SESSION MANAGEMENT:
- Each store requires individual context establishment
- Process: Store page → GET /accueil → POST /accueil → API calls
- Maintains cookies and headers throughout session

🎯 PROVEN RESULTS:
------------------

VALIDATION METRICS:
- ✅ 1,068 EAN codes successfully scraped
- ✅ 100% API compatibility across 10 test stores
- ✅ Price variations detected: 4.1% to 24.8%
- ✅ Geographic coverage: Nationwide France
- ✅ Real-time data: Current prices and stock levels

EXAMPLE PRICE VARIATIONS FOUND:
- Nivea Care Cream: €3.22 - €4.02 (24.8% difference)
- Nivea Hydrating Cream: €1.87 - €2.30 (23.0% difference)  
- M&M's Peanuts: €0.94 - €1.13 (20.2% difference)

================================================================================
                                SCALING POTENTIAL
================================================================================

🚀 PRODUCTION DEPLOYMENT:
-------------------------

SEARCH TERMS EXPANSION:
- Current: "lait" (1,068 EANs)
- Potential: "pain", "beurre", "fromage", "yaourt", etc.
- Estimated: 10,000+ EANs per term = 100,000+ total EANs

STORE COVERAGE:
- Current: 10 stores tested
- Available: 2,086 stores in store_mappings_clean.csv
- Potential: Complete France coverage

PRICE DATABASE:
- Current: 1,000 price points tested
- Potential: 100,000 EANs × 2,086 stores = 208 million price points
- Update frequency: Real-time via API

💡 USE CASES:
-------------

CONSUMER APPLICATIONS:
- Price comparison website/app
- Best deal finder by location
- Shopping optimization tool

BUSINESS INTELLIGENCE:
- Competitive pricing analysis
- Regional pricing strategy insights
- Market research and trends

RESEARCH APPLICATIONS:
- Economic studies on regional pricing
- Consumer behavior analysis
- Retail market dynamics

================================================================================
                                TECHNICAL NOTES
================================================================================

🔧 DEPENDENCIES:
- Python 3.7+
- Camoufox browser (for anti-detection)
- requests library (for API calls)
- Standard libraries: json, csv, logging, time

⚠️ IMPORTANT CONSIDERATIONS:
- Respect rate limits (built-in delays)
- Use responsibly for research/personal use
- Store context establishment is critical for API success
- EAN format: 13-digit codes (leading zeros preserved)

🛠️ TROUBLESHOOTING:
- Check html_logs/ for debugging information
- Verify store IDs in store_mappings_clean.csv
- Ensure proper session establishment before API calls
- Monitor for anti-bot detection and adjust delays

================================================================================
                                    CONCLUSION
================================================================================

This project demonstrates a complete, production-ready system for comprehensive
price comparison across France's largest supermarket chain. The combination of
browser-based scraping and API validation provides both scale and accuracy,
making it suitable for consumer applications, business intelligence, and 
research purposes.

The system has been validated with real data showing significant price 
variations across stores, proving its effectiveness for price comparison
applications.

For questions or improvements, refer to the individual script files which
contain detailed comments and logging for further understanding.

================================================================================
