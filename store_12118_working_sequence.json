[{"timestamp": "2025-07-06T13:05:42.710Z", "url": "https://www.intermarche.com/magasins/12118/marseille-13004/infos-pratiques", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/magasins/12118/marseille-13004/infos-pratiques"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=0, i"}, {"name": "referer", "value": "https://www.intermarche.com/enseigne/magazine/tous-les-magasins?srsltid=AfmBOoo5epBRN3vvZdoZ3d5Fz568hZk6S4gq2ypJg1PiM4USfP8TpCXL"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "document"}, {"name": "sec-fetch-mode", "value": "navigate"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "sec-fetch-user", "value": "?1"}, {"name": "upgrade-insecure-requests", "value": "1"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "8867"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=10800"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-length", "value": "66328"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "text/html; charset=utf-8"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:42 GMT"}, {"name": "link", "value": "<https://cdn.intermarche.com/web/assets/87-0-0-9/_next/static/css/2ffc7fd5fcef7e73.css>; rel=preload; as=\"style\", <https://cdn.intermarche.com/web/assets/87-0-0-9/_next/static/css/abfb072d8e792b81.css>; rel=preload; as=\"style\", <https://cdn.intermarche.com/web/assets/87-0-0-9/_next/static/css/26f1fb249b4f4885.css>; rel=preload; as=\"style\", <https://cdn.intermarche.com/web/assets/87-0-0-9/_next/static/css/40b7f2d01c81f9f7.css>; rel=preload; as=\"style\""}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "hit"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": "12118-disconnected-desktop"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:44.075Z", "url": "https://www.intermarche.com/api/pdv-info?pdvRef=12118", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/pdv-info?pdvRef=12118"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/magasins/12118/marseille-13004/infos-pratiques"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "7533"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=10800"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-length", "value": "3348"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:44 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "hit"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:44.180Z", "url": "https://www.intermarche.com/api/service/gasstation/v1/gas_stations/12118", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/service/gasstation/v1/gas_stations/12118"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-type", "value": "application/json"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/magasins/12118/marseille-13004/infos-pratiques"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "84f4720be6180eef"}, {"name": "x-b3-traceid", "value": "1727adb0b247636b99097eb0e37874de"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "gasstation"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:44 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "1727adb0b247636b99097eb0e37874de"}]}, {"timestamp": "2025-07-06T13:05:44.181Z", "url": "https://www.intermarche.com/api/service/gasstation/v1/gas_stations/12118/prices", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/service/gasstation/v1/gas_stations/12118/prices"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-type", "value": "application/json"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/magasins/12118/marseille-13004/infos-pratiques"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "260a4e522dbcc944"}, {"name": "x-b3-traceid", "value": "9ccbfa746b790a0d6069c4f6245e6847"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "gasstation"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "content-length", "value": "440"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:44 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "9ccbfa746b790a0d6069c4f6245e6847"}]}, {"timestamp": "2025-07-06T13:05:44.197Z", "url": "https://www.intermarche.com/api/service/creneaux/v1/pdvs/12118/prochains-creneaux", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/service/creneaux/v1/pdvs/12118/prochains-creneaux"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-type", "value": "application/json"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/magasins/12118/marseille-13004/infos-pratiques"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "758b67ac091874f4"}, {"name": "x-b3-traceid", "value": "4bef0495b9098e0d39ffc57599bf624b"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "creneaux"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:44 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "4bef0495b9098e0d39ffc57599bf624b"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:46.800Z", "url": "https://www.intermarche.com/api/categories?pdvRef=12118&maxDepth=4", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/categories?pdvRef=12118&maxDepth=4"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/accueil"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "0"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=undefined, max-age=undefined"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:47 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "miss"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:46.804Z", "url": "https://www.intermarche.com/api/pdv-info?pdvRef=12118", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/pdv-info?pdvRef=12118"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/accueil"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "7536"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=10800"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-length", "value": "3348"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:46 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "hit"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:46.902Z", "url": "https://www.intermarche.com/api/service/creneaux/v1/pdvs/12118/prochains-creneaux", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/service/creneaux/v1/pdvs/12118/prochains-creneaux"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-type", "value": "application/json"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/accueil"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "ce2debae653595f9"}, {"name": "x-b3-traceid", "value": "ba00e15d77838572e54b4febe82442f1"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "creneaux"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:47 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "ba00e15d77838572e54b4febe82442f1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:47.288Z", "url": "https://www.intermarche.com/api/service/produits/v1/pdvs/12118/products/suggestion", "method": "POST", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/service/produits/v1/pdvs/12118/products/suggestion"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-length", "value": "69"}, {"name": "content-type", "value": "application/json"}, {"name": "origin", "value": "https://www.intermarche.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/accueil"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "200573b4be4f292d"}, {"name": "x-b3-traceid", "value": "fa7b4103c87a78f15a7f4832045ccdfa"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "produits"}], "postData": {"mimeType": "application/json", "text": "{\"productNumber\":16,\"criterias\":[{\"type\":\"boutique\",\"value\":\"4653\"}]}"}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:47 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "fa7b4103c87a78f15a7f4832045ccdfa"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:50.479Z", "url": "https://www.intermarche.com/api/categories?pdvRef=12118&maxDepth=4", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/categories?pdvRef=12118&maxDepth=4"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "0"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=undefined, max-age=undefined"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:50 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "miss"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:50.497Z", "url": "https://www.intermarche.com/api/pdv-info?pdvRef=12118", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/pdv-info?pdvRef=12118"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "1557"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=10800"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-length", "value": "3348"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:50 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "hit"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:52.599Z", "url": "https://www.intermarche.com/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory", "method": "POST", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-length", "value": "103"}, {"name": "content-type", "value": "application/json"}, {"name": "origin", "value": "https://www.intermarche.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "4fc6582400b21a0d"}, {"name": "x-b3-traceid", "value": "441a201024bb6f3fee6c2963e5bbd7b2"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-tracking-last-page-id", "value": "https://www.intermarche.com/accueil"}, {"name": "x-itm-tracking-origin-type", "value": "ADD_TO_BASKET"}, {"name": "x-itm-tracking-page-id", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "x-itm-tracking-page-url", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "produits"}], "postData": {"mimeType": "application/json", "text": "{\"keyword\":\"lait\",\"page\":1,\"size\":40,\"filtres\":[],\"tri\":\"pertinence\",\"ordreTri\":null,\"catalog\":[\"PDV\"]}"}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:05:53 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "441a201024bb6f3fee6c2963e5bbd7b2"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:05:59.709Z", "url": "https://www.intermarche.com/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory", "method": "POST", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-length", "value": "103"}, {"name": "content-type", "value": "application/json"}, {"name": "origin", "value": "https://www.intermarche.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "07c55f00f958a330"}, {"name": "x-b3-traceid", "value": "b076ade2c200e69b9068b571639c1ad7"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-tracking-last-page-id", "value": "https://www.intermarche.com/accueil"}, {"name": "x-itm-tracking-origin-type", "value": "ADD_TO_BASKET"}, {"name": "x-itm-tracking-page-id", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "x-itm-tracking-page-url", "value": "https://www.intermarche.com/recherche/lait"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "produits"}], "postData": {"mimeType": "application/json", "text": "{\"keyword\":\"lait\",\"page\":2,\"size\":40,\"filtres\":[],\"tri\":\"pertinence\",\"ordreTri\":null,\"catalog\":[\"PDV\"]}"}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:06:00 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "b076ade2c200e69b9068b571639c1ad7"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:06:05.602Z", "url": "https://www.intermarche.com/api/categories?pdvRef=12118&maxDepth=4", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/categories?pdvRef=12118&maxDepth=4"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "0"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=undefined, max-age=undefined"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:06:05 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "miss"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:06:05.615Z", "url": "https://www.intermarche.com/api/pdv-info?pdvRef=12118", "method": "GET", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/pdv-info?pdvRef=12118"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}], "postData": {}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "accept-ranges", "value": "bytes"}, {"name": "age", "value": "1572"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "public, no-transform, s-maxage=10800"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-length", "value": "3348"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:06:05 GMT"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-cache", "value": "hit"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-custom-hash", "value": ""}, {"name": "x-datadome", "value": "protected"}, {"name": "x-device-type", "value": "desktop"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "x-ua-compatible", "value": "IE=Edge,chrome=1"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:06:06.792Z", "url": "https://www.intermarche.com/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory", "method": "POST", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-length", "value": "103"}, {"name": "content-type", "value": "application/json"}, {"name": "origin", "value": "https://www.intermarche.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "8f70940e05b0c863"}, {"name": "x-b3-traceid", "value": "c9983e12cc9e85bc622316ad1540f36d"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-tracking-last-page-id", "value": "https://www.intermarche.com/recherche/lait?page=2"}, {"name": "x-itm-tracking-origin-type", "value": "SEARCH"}, {"name": "x-itm-tracking-page-id", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "x-itm-tracking-page-url", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "produits"}], "postData": {"mimeType": "application/json", "text": "{\"keyword\":\"oeuf\",\"page\":1,\"size\":40,\"filtres\":[],\"tri\":\"pertinence\",\"ordreTri\":null,\"catalog\":[\"PDV\"]}"}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:06:07 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "c9983e12cc9e85bc622316ad1540f36d"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}, {"timestamp": "2025-07-06T13:06:10.662Z", "url": "https://www.intermarche.com/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory", "method": "POST", "status": 200, "headers": [{"name": ":authority", "value": "www.intermarche.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/service/produits/v4/pdvs/12118/products/byKeywordAndCategory"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "fr-FR,fr;q=0.9"}, {"name": "content-length", "value": "103"}, {"name": "content-type", "value": "application/json"}, {"name": "origin", "value": "https://www.intermarche.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "sec-ch-device-memory", "value": "8"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""}, {"name": "sec-ch-ua-arch", "value": "\"x86\""}, {"name": "sec-ch-ua-full-version-list", "value": "\"Not)A;Brand\";v=\"*******\", \"Chromium\";v=\"138.0.7204.97\", \"Google Chrome\";v=\"138.0.7204.97\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-model", "value": "\"\""}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-b3-spanid", "value": "4fc8772043168800"}, {"name": "x-b3-traceid", "value": "736073168296b38069bc0a11f4085e4a"}, {"name": "x-is-server", "value": "false"}, {"name": "x-itm-device-fp", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-session-id", "value": "b71e80c4-dcbf-4f04-93ff-b20094070d18"}, {"name": "x-itm-tracking-last-page-id", "value": "https://www.intermarche.com/recherche/lait?page=2"}, {"name": "x-itm-tracking-origin-type", "value": "SEARCH"}, {"name": "x-itm-tracking-page-id", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "x-itm-tracking-page-url", "value": "https://www.intermarche.com/recherche/oeuf"}, {"name": "x-itm-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36"}, {"name": "x-optional-oauth", "value": "true"}, {"name": "x-red-device", "value": "red_fo_desktop"}, {"name": "x-red-version", "value": "3"}, {"name": "x-service-name", "value": "produits"}], "postData": {"mimeType": "application/json", "text": "{\"keyword\":\"oeuf\",\"page\":2,\"size\":40,\"filtres\":[],\"tri\":\"pertinence\",\"ordreTri\":null,\"catalog\":[\"PDV\"]}"}, "response_headers": [{"name": "accept-ch", "value": "Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "content-encoding", "value": "br"}, {"name": "content-security-policy", "value": "frame-ancestors 'self'  https://app.contentful.com"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Sun, 06 Jul 2025 13:06:11 GMT"}, {"name": "expires", "value": "0"}, {"name": "pragma", "value": "no-cache"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 google"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-datadome", "value": "protected"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-powered-by", "value": "Express"}, {"name": "x-red-traceid", "value": "736073168296b38069bc0a11f4085e4a"}, {"name": "x-xss-protection", "value": "1; mode=block"}]}]