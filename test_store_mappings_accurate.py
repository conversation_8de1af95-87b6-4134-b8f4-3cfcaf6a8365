#!/usr/bin/env python3
"""
Test EAN-based API approach with accurate context establishment sequence
"""

import requests
import json
import time
from datetime import datetime

def establish_store_context(session, store_id, city_code):
    """Establish store context following the exact sequence from comprehensive test"""
    
    # Headers for store page access
    store_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        # Step 1: Access store page
        store_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/infos-pratiques"
        response = session.get(store_url, headers=store_headers, timeout=10)
        
        if response.status_code != 200:
            return False, f"Store page failed: {response.status_code}"
        
        # Step 2: GET accueil (homepage) to establish context
        accueil_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/accueil"
        response = session.get(accueil_url, headers=store_headers, timeout=10)
        
        if response.status_code != 200:
            return False, f"Accueil GET failed: {response.status_code}"
        
        # Step 3: POST to accueil with Next.js action
        post_headers = store_headers.copy()
        post_headers.update({
            'Accept': 'text/x-component',
            'Content-Type': 'text/plain;charset=UTF-8',
            'Next-Action': '6b0b5b5b8b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b',
            'Next-Router-State-Tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22magasins%22%2C%7B%22children%22%3A%5B%22%5Bslug%5D%22%2C%7B%22children%22%3A%5B%22%5Bslug%5D%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%7D%5D%7D%5D%7D%5D%7D%5D%7D%5D',
            'Referer': accueil_url,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        post_data = f'["{store_id}"]'
        response = session.post(accueil_url, headers=post_headers, data=post_data, timeout=10)
        
        if response.status_code != 200:
            return False, f"Accueil POST failed: {response.status_code}"
        
        return True, "Context established successfully"
        
    except Exception as e:
        return False, f"Error: {str(e)}"

def test_ean_api(session, store_id):
    """Test EAN-based API access"""
    
    # API headers
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'x-red-version': '3',
        'x-itm-device-fp': 'desktop',
        'x-itm-session-id': f'session_{int(time.time())}',
        'x-custom-hash': f'{store_id}-disconnected-desktop',
        'Origin': 'https://www.intermarche.com',
        'Referer': f'https://www.intermarche.com/magasins/{store_id}/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    try:
        # Test suggestion API with a working category
        suggestion_url = f"https://www.intermarche.com/api/service/produits/v1/pdvs/{store_id}/products/suggestion"
        suggestion_data = {
            "query": "lait",
            "category": "4653",  # Use a category that works
            "limit": 5
        }
        
        response = session.post(suggestion_url, headers=api_headers, json=suggestion_data, timeout=10)
        
        if response.status_code != 200:
            return False, f"Suggestion API failed: {response.status_code}"
        
        result = response.json()
        suggestions = result.get('suggestions', [])
        
        if not suggestions:
            return False, "No suggestions returned"
        
        # Extract EAN codes
        ean_codes = []
        for suggestion in suggestions[:3]:
            if 'ean' in suggestion:
                ean_codes.append(suggestion['ean'])
        
        if not ean_codes:
            return False, "No EAN codes found"
        
        # Test byEans API
        byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{store_id}/products/byEans"
        byeans_data = {"eans": ean_codes}
        
        response = session.post(byeans_url, headers=api_headers, json=byeans_data, timeout=10)
        
        if response.status_code != 200:
            return False, f"ByEans API failed: {response.status_code}"
        
        result = response.json()
        products = result.get('products', [])
        
        if not products:
            return False, "No products returned"
        
        return True, f"Success: {len(products)} products retrieved"
        
    except Exception as e:
        return False, f"API Error: {str(e)}"

def main():
    """Test multiple stores with accurate context establishment"""
    
    # Load store mappings
    try:
        with open('store_mappings_clean.csv', 'r', encoding='utf-8') as f:
            store_mappings = []
            for line in f:
                line = line.strip()
                if line and ',' in line:
                    store_id, city_code = line.split(',', 1)
                    store_mappings.append((store_id, city_code))
    except FileNotFoundError:
        print("Error: store_mappings_clean.csv not found.")
        return
    
    print(f"Loaded {len(store_mappings)} store mappings")
    
    # Test a small sample including our known working store
    known_working = [('12118', 'marseille-13004')]
    test_stores = known_working + store_mappings[:4]  # Test 5 stores total
    
    results = {
        'successful_stores': [],
        'failed_stores': [],
        'context_failures': [],
        'api_failures': []
    }
    
    for i, (store_id, city_code) in enumerate(test_stores, 1):
        print(f"\n=== Testing Store {i}/{len(test_stores)}: {store_id} ({city_code}) ===")
        
        # Create new session for each store
        session = requests.Session()
        
        # Test context establishment
        context_success, context_message = establish_store_context(session, store_id, city_code)
        print(f"Context: {context_message}")
        
        if not context_success:
            results['context_failures'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': context_message
            })
            results['failed_stores'].append(store_id)
            continue
        
        # Test API access
        api_success, api_message = test_ean_api(session, store_id)
        print(f"API: {api_message}")
        
        if not api_success:
            results['api_failures'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': api_message
            })
            results['failed_stores'].append(store_id)
        else:
            results['successful_stores'].append({
                'store_id': store_id,
                'city_code': city_code
            })
        
        # Delay between stores
        time.sleep(5)
    
    # Save and display results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"accurate_store_test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== SUMMARY ===")
    print(f"Total stores tested: {len(test_stores)}")
    print(f"Successful stores: {len(results['successful_stores'])}")
    print(f"Failed stores: {len(results['failed_stores'])}")
    print(f"Success rate: {len(results['successful_stores'])/len(test_stores)*100:.1f}%")
    print(f"\nResults saved to: {results_file}")
    
    if results['successful_stores']:
        print(f"\nWorking stores:")
        for store in results['successful_stores']:
            print(f"  - {store['store_id']} ({store['city_code']})")

if __name__ == "__main__":
    main()
