#!/usr/bin/env python3
"""
EAN Collector Scraper for Intermarché
Scrapes product pages to collect thousands of EANs for multi-store comparison
Includes feedback loop with HTML debugging capabilities
"""

import requests
import json
import csv
import time
import logging
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs
from datetime import datetime
import os

# Setup logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ean_collector.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
log = logging.getLogger(__name__)

class EANCollectorScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.base_url = "https://www.intermarche.com"
        self.collected_eans = set()
        self.debug_counter = 0
        
        # Search terms to use for product discovery
        self.search_terms = [
            "lait", "pain", "eau", "yaourt", "fromage", "viande", "poisson", 
            "fruits", "légumes", "riz", "pâtes", "huile", "beurre", "oeufs",
            "chocolat", "café", "thé", "sucre", "farine", "tomates", "pommes",
            "bananes", "carottes", "pommes de terre", "salade", "jambon",
            "poulet", "boeuf", "saumon", "crevettes", "pizza", "soupe"
        ]
        
    def save_debug_html(self, html_content, filename_suffix=""):
        """Save HTML content for debugging"""
        self.debug_counter += 1
        filename = f"debug_html_{self.debug_counter}_{filename_suffix}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        log.info(f"🐛 Debug HTML saved: {filename}")
        return filename
        
    def establish_store_context(self, store_id, store_city):
        """Establish context for a specific store"""
        log.info(f"Establishing context for store {store_id} ({store_city})")

        # Step 1: Get store page
        store_url = f"{self.base_url}/magasins/{store_id}/{store_city}/infos-pratiques"
        log.info(f"Getting store page: {store_url}")
        
        try:
            response = self.session.get(store_url, timeout=30)
            log.info(f"Store page response: {response.status_code}")
            
            if response.status_code != 200:
                log.error(f"❌ Failed to get store page: {response.status_code}")
                return False
                
            # Step 2: GET accueil
            accueil_url = f"{self.base_url}/accueil"
            response = self.session.get(accueil_url, timeout=30)
            log.info(f"GET Accueil response: {response.status_code}")
            
            # Step 3: POST accueil
            post_data = {"storeId": store_id}
            response = self.session.post(accueil_url, data=post_data, timeout=30)
            log.info(f"POST Accueil response: {response.status_code}")
            
            if response.status_code == 200:
                log.info("✅ Store context established successfully")
                return True
            else:
                log.error(f"❌ Failed to establish store context: {response.status_code}")
                return False
                
        except Exception as e:
            log.error(f"❌ Error establishing store context: {e}")
            return False
    
    def search_products(self, search_term, max_pages=5):
        """Search for products and collect EANs from search results"""
        log.info(f"🔍 Searching for: '{search_term}'")
        collected_eans = set()
        
        for page in range(1, max_pages + 1):
            log.info(f"📄 Processing page {page}/{max_pages}")
            
            # Try different search URL patterns
            search_urls = [
                f"{self.base_url}/recherche?q={search_term}&page={page}",
                f"{self.base_url}/search?query={search_term}&page={page}",
                f"{self.base_url}/produits/recherche?q={search_term}&page={page}"
            ]
            
            for search_url in search_urls:
                try:
                    log.info(f"🌐 Trying URL: {search_url}")
                    response = self.session.get(search_url, timeout=30)
                    log.info(f"Search response: {response.status_code}")
                    
                    if response.status_code == 200:
                        # Save debug HTML
                        debug_file = self.save_debug_html(
                            response.text, 
                            f"search_{search_term}_page_{page}"
                        )
                        
                        # Parse the HTML
                        eans_found = self.extract_eans_from_html(response.text, search_term, page)
                        collected_eans.update(eans_found)
                        
                        if eans_found:
                            log.info(f"✅ Found {len(eans_found)} EANs on page {page}")
                            break  # Success with this URL pattern
                        else:
                            log.info(f"⚠️ No EANs found on page {page}, trying next URL pattern")
                    
                except Exception as e:
                    log.error(f"❌ Error searching page {page}: {e}")
                    continue
            
            # Add delay between pages
            time.sleep(2)
        
        log.info(f"🎯 Total EANs collected for '{search_term}': {len(collected_eans)}")
        return collected_eans
    
    def extract_eans_from_html(self, html_content, search_term, page):
        """Extract EANs from HTML content using multiple strategies"""
        eans = set()
        soup = BeautifulSoup(html_content, 'html.parser')
        
        log.info(f"🔍 Analyzing HTML for EANs (search: {search_term}, page: {page})")
        
        # Strategy 1: Look for EAN in data attributes
        for element in soup.find_all(attrs={'data-ean': True}):
            ean = element.get('data-ean')
            if self.is_valid_ean(ean):
                eans.add(ean)
                log.info(f"📦 Found EAN in data-ean: {ean}")
        
        # Strategy 2: Look for EAN in product IDs
        for element in soup.find_all(attrs={'data-product-id': True}):
            product_id = element.get('data-product-id')
            if self.is_valid_ean(product_id):
                eans.add(product_id)
                log.info(f"📦 Found EAN in product-id: {product_id}")
        
        # Strategy 3: Look for EAN in class names or IDs
        ean_pattern = re.compile(r'\b\d{13}\b')  # 13-digit EAN pattern
        
        # Check all text content for EAN patterns
        text_content = soup.get_text()
        ean_matches = ean_pattern.findall(text_content)
        for match in ean_matches:
            if self.is_valid_ean(match):
                eans.add(match)
                log.info(f"📦 Found EAN in text: {match}")
        
        # Strategy 4: Look in script tags for JSON data
        for script in soup.find_all('script'):
            if script.string:
                script_eans = ean_pattern.findall(script.string)
                for ean in script_eans:
                    if self.is_valid_ean(ean):
                        eans.add(ean)
                        log.info(f"📦 Found EAN in script: {ean}")
        
        # Strategy 5: Look for product URLs that might contain EANs
        for link in soup.find_all('a', href=True):
            href = link['href']
            url_eans = ean_pattern.findall(href)
            for ean in url_eans:
                if self.is_valid_ean(ean):
                    eans.add(ean)
                    log.info(f"📦 Found EAN in URL: {ean}")
        
        # Log HTML structure for debugging
        self.log_html_structure(soup, search_term, page)
        
        return eans
    
    def log_html_structure(self, soup, search_term, page):
        """Log HTML structure for debugging purposes"""
        log.info(f"🔍 HTML Structure Analysis for {search_term} page {page}:")
        
        # Check for common product container patterns
        product_containers = [
            soup.find_all('div', class_=re.compile(r'product', re.I)),
            soup.find_all('article', class_=re.compile(r'product', re.I)),
            soup.find_all('li', class_=re.compile(r'product', re.I)),
            soup.find_all('div', class_=re.compile(r'item', re.I)),
        ]
        
        for container_type, containers in enumerate(product_containers):
            if containers:
                log.info(f"  📦 Found {len(containers)} potential product containers (type {container_type})")
                
                # Log first container structure
                if containers:
                    first_container = containers[0]
                    log.info(f"  📋 First container classes: {first_container.get('class', [])}")
                    log.info(f"  📋 First container attributes: {list(first_container.attrs.keys())}")
        
        # Check for pagination
        pagination = soup.find_all(['nav', 'div'], class_=re.compile(r'pag', re.I))
        if pagination:
            log.info(f"  📄 Found pagination elements: {len(pagination)}")
        
        # Check for search results info
        results_info = soup.find_all(text=re.compile(r'résultat|produit|trouvé', re.I))
        if results_info:
            log.info(f"  📊 Found results info: {results_info[:3]}")  # First 3 matches
    
    def is_valid_ean(self, ean_string):
        """Validate EAN format"""
        if not ean_string or not isinstance(ean_string, str):
            return False
        
        # Remove any whitespace
        ean = ean_string.strip()
        
        # Check if it's exactly 13 digits
        if not re.match(r'^\d{13}$', ean):
            return False
        
        # Basic EAN-13 checksum validation
        try:
            digits = [int(d) for d in ean]
            checksum = sum(digits[i] if i % 2 == 0 else digits[i] * 3 for i in range(12))
            return (10 - (checksum % 10)) % 10 == digits[12]
        except:
            return False
    
    def save_eans_to_file(self, eans, filename=None):
        """Save collected EANs to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"collected_eans_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['EAN', 'Collection_Time'])
            
            for ean in sorted(eans):
                writer.writerow([ean, datetime.now().isoformat()])
        
        log.info(f"💾 Saved {len(eans)} EANs to {filename}")
        return filename

def main():
    """Main execution function"""
    scraper = EANCollectorScraper()
    
    # Load store mappings
    log.info("Loading store mappings...")
    stores = []
    try:
        with open('store_mappings_clean.csv', 'r', encoding='utf-8-sig') as f:  # Handle BOM
            reader = csv.reader(f)
            for row in reader:
                if len(row) >= 2:
                    # Clean store ID and city from any BOM or whitespace
                    store_id = row[0].strip().replace('\ufeff', '')
                    store_city = row[1].strip()
                    stores.append((store_id, store_city))
    except FileNotFoundError:
        log.error("store_mappings_clean.csv not found!")
        return
    
    log.info(f"📊 Loaded {len(stores)} stores")
    
    # Use first store for testing
    if stores:
        store_id, store_city = stores[0]  # Use first store
        log.info(f"🎯 Testing with store: {store_id} ({store_city})")
        
        # Establish store context
        if scraper.establish_store_context(store_id, store_city):
            
            # Collect EANs from multiple search terms
            all_eans = set()
            
            # Start with a few search terms for testing
            test_terms = scraper.search_terms[:3]  # First 3 terms
            
            for search_term in test_terms:
                log.info(f"\n{'='*60}")
                log.info(f"🔍 SEARCHING FOR: {search_term}")
                log.info(f"{'='*60}")
                
                eans = scraper.search_products(search_term, max_pages=2)
                all_eans.update(eans)
                
                log.info(f"✅ Collected {len(eans)} EANs for '{search_term}'")
                log.info(f"📊 Total unique EANs so far: {len(all_eans)}")
                
                # Save progress
                if all_eans:
                    scraper.save_eans_to_file(all_eans)
                
                # Delay between searches
                time.sleep(5)
            
            # Final summary
            log.info(f"\n{'='*60}")
            log.info(f"🎉 COLLECTION COMPLETE")
            log.info(f"{'='*60}")
            log.info(f"📊 Total unique EANs collected: {len(all_eans)}")
            
            if all_eans:
                final_file = scraper.save_eans_to_file(all_eans, "final_ean_collection.csv")
                log.info(f"💾 Final EAN collection saved to: {final_file}")
                
                # Show sample EANs
                sample_eans = list(all_eans)[:10]
                log.info(f"📦 Sample EANs: {sample_eans}")
            else:
                log.warning("⚠️ No EANs collected - check debug HTML files for analysis")
        
        else:
            log.error("❌ Failed to establish store context")
    else:
        log.error("❌ No stores loaded from CSV")

if __name__ == "__main__":
    main()
