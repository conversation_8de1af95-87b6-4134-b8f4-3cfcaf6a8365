import time
import json
import os
from datetime import datetime
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
# This is the correct import for modern versions of the library
from playwright_stealth import stealth_sync

def save_html_log(content, filename_suffix):
    """Save HTML content to a log file for debugging"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"html_log_{timestamp}_{filename_suffix}.html"

    os.makedirs("html_logs", exist_ok=True)
    filepath = os.path.join("html_logs", filename)

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"📄 HTML saved to: {filepath}")
    return filepath

def handle_captcha(page):
    """Checks for a CAPTCHA and waits for the user to solve it."""
    print("🛡️ Checking for CAPTCHA...")
    # Selectors for common CAPTCHA providers like hCaptcha or reCAPTCHA
    captcha_selectors = [
        "iframe[title*='hCaptcha']",
        "iframe[title*='reCAPTCHA']",
        "iframe[src*='captcha']",
        "//div[contains(., 'Vérifiez que vous n'êtes pas un robot')]"
    ]
    
    for selector in captcha_selectors:
        try:
            # Wait for a short period to see if the CAPTCHA appears
            captcha_frame = page.locator(selector).first
            if captcha_frame.is_visible(timeout=5000):
                print(f"🛡️ CAPTCHA detected with selector: {selector}")
                print("✋ Please solve the CAPTCHA in the browser window.")
                print("   The script will automatically resume once the CAPTCHA is solved.")
                
                # Wait indefinitely (timeout=0) for the CAPTCHA to disappear
                captcha_frame.wait_for(state='hidden', timeout=0) 
                
                print("✅ CAPTCHA solved. Resuming...")
                time.sleep(3)  # Wait a bit for the page to reload/settle
                return True # Exit after handling the first found CAPTCHA
        except PlaywrightTimeoutError:
            # This is expected if the CAPTCHA doesn't exist for this selector
            continue
    
    print("ℹ️ No CAPTCHA detected.")
    return False

def handle_cookie_consent(page):
    """Handle cookie consent popup (usually from Didomi)"""
    print("🍪 Checking for cookie consent popup...")
    
    # Give the banner a moment to appear after page load or a CAPTCHA solve
    time.sleep(2)
    save_html_log(page.content(), "before_cookie_consent")

    # Common cookie consent selectors for Intermarché (Didomi is primary)
    cookie_selectors = [
        "#didomi-notice-agree-button",      # Primary Didomi selector
        "button:has-text('Tout accepter')", # Common text
        "button:has-text('Accepter et fermer')",
        "[data-testid='agree-button']",
        "button[aria-label*='Accepter']",
        "button[aria-label*='Accept']",
        "button:has-text('Accepter')",
        "button:has-text('Accept')",
        ".didomi-continue-without-agreeing",
        "#onetrust-accept-btn-handler",
    ]

    for selector in cookie_selectors:
        try:
            button = page.locator(selector).first
            # Check if the button is visible on the page before trying to click
            if button.is_visible(timeout=3000):
                print(f"🍪 Found cookie button with selector: {selector}")
                button.click(timeout=5000)
                print("✅ Cookie consent accepted.")
                # Wait for the overlay to disappear
                page.wait_for_timeout(3000) 
                save_html_log(page.content(), "after_cookie_consent")
                return True
        except PlaywrightTimeoutError:
            # This is expected if the selector is not found or not visible in time
            # print(f"  - Selector '{selector}' not found or not visible in time.")
            continue
        except Exception as e:
            print(f"⚠️ Failed to click cookie button {selector}: {e}")
            continue

    print("ℹ️ No cookie consent popup found or couldn't handle it.")
    save_html_log(page.content(), "no_cookie_consent_found")
    return False

def set_store_context(page, store_url):
    """
    Navigates to the store page, handles CAPTCHA and cookies, and then
    clicks the 'Faire mes courses' button to set the session.
    """
    print(f"Navigating to store page: {store_url}")
    try:
        # Go to the page and wait until the DOM is loaded
        page.goto(store_url, wait_until="domcontentloaded", timeout=60000)
        save_html_log(page.content(), "store_page_initial")

        # Step 1: Handle potential CAPTCHA first, as it blocks everything else
        handle_captcha(page)

        # Step 2: Handle cookie consent after the CAPTCHA is gone
        handle_cookie_consent(page)

        # Wait a moment for any overlays to disappear
        time.sleep(3)
        save_html_log(page.content(), "store_page_after_popups")

        # Define the locator for the button. Playwright's locators auto-wait.
        shopping_button_locator = page.locator("button:has-text('Faire mes courses')")

        # Check if button is visible
        if not shopping_button_locator.first.is_visible(timeout=10000):
            print("⚠️ 'Faire mes courses' button not found, trying alternatives...")
            alternative_selectors = [
                "//button[.//div[contains(text(), 'Faire mes courses')]]",
                "[data-testid*='shopping']",
                "button[aria-label*='courses']",
                "a:has-text('Faire mes courses')"
            ]
            for selector in alternative_selectors:
                if page.locator(selector).first.is_visible(timeout=2000):
                    print(f"✅ Found button with alternative selector: {selector}")
                    shopping_button_locator = page.locator(selector).first
                    break
            else:
                print("❌ Could not find 'Faire mes courses' button with any selector.")
                save_html_log(page.content(), "store_page_no_button_found")
                return False

        print("✅ Found 'Faire mes courses' button. Clicking...")
        shopping_button_locator.click(timeout=20000)

        # Wait for the URL to change, indicating a successful navigation
        page.wait_for_url("**/accueil", timeout=20000)
        print("✅ Successfully set store context.")
        save_html_log(page.content(), "store_context_success")
        time.sleep(3) # Give the page a moment to settle
        return True

    except PlaywrightTimeoutError as e:
        print(f"❌ Error: A timeout occurred while setting the store context. {e}")
        save_html_log(page.content(), "store_context_timeout_error")
        return False
    except Exception as e:
        print(f"❌ An unexpected error occurred while setting store context: {e}")
        save_html_log(page.content(), "store_context_unexpected_error")
        return False

def scroll_to_bottom(page):
    """Scrolls to the bottom of the page to trigger dynamic content loading."""
    print("📜 Scrolling to load all products...")
    last_height = page.evaluate("document.body.scrollHeight")
    while True:
        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        try:
            # Wait for the network to be idle, a good sign that new content has loaded
            page.wait_for_load_state("networkidle", timeout=5000)
        except PlaywrightTimeoutError:
            # If the network doesn't become idle, we'll break after a short wait
            print("   - Network idle timeout, continuing scroll check...")
            time.sleep(2)

        new_height = page.evaluate("document.body.scrollHeight")
        if new_height == last_height:
            print("✅ Reached the bottom of the page.")
            break
        last_height = new_height

def scrape_products_from_search(page, search_term):
    """Navigates to a search results page, scrolls, and scrapes product data."""
    search_url = f"https://www.intermarche.com/recherche/{search_term}"
    print(f"\n🔎 Searching for: '{search_term}'")

    try:
        page.goto(search_url, wait_until="domcontentloaded", timeout=60000)
        save_html_log(page.content(), f"search_{search_term}_initial")

        # Wait for the product grid to appear
        page.wait_for_selector(".stime-product-list__grid", timeout=20000)

    except PlaywrightTimeoutError:
        print(f"❌ No results found or page timed out for '{search_term}'.")
        save_html_log(page.content(), f"search_{search_term}_timeout_error")
        return []

    scroll_to_bottom(page)

    products_data = []
    # Locate all product tiles.
    product_tiles = page.locator(".stime-product-list__item").all()
    print(f"Found {len(product_tiles)} product tiles for '{search_term}'.")

    for tile in product_tiles:
        try:
            # The EAN is in the href of the product link
            link_element = tile.locator(".productCard__link")
            href = link_element.get_attribute('href')
            if not href:
                continue
            
            ean = href.split('/')[-1]
            
            # Basic validation to ensure it looks like an EAN
            if ean.isdigit() and (len(ean) == 13 or len(ean) == 8):
                name_element = tile.locator(".stime-product--details__title")
                name = name_element.inner_text().strip()
                
                products_data.append({
                    "name": name,
                    "ean": ean,
                    "search_term": search_term
                })
        except Exception:
            # This tile might be an ad or something else. Skip silently.
            continue
            
    return products_data

def main():
    # --- Configuration ---
    STORE_URL = "https://www.intermarche.com/magasins/11621/villeneuve-loubet-06270/infos-pratiques"
    SEARCH_QUERIES = ["lait", "beurre", "pain", "eau", "fromage", "yaourt", "jambon", "pates", "riz", "chocolat", "cafe", "sucre", "farine", "huile"]
    OUTPUT_FILENAME = "intermarche_products_playwright.json"
    
    all_products = []
    scraped_eans = set()

    with sync_playwright() as p:
        # Launch a browser. headless=False is required for manual CAPTCHA solving.
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        context = browser.new_context(no_viewport=True)
        page = context.new_page()
        
        # Apply stealth patches to make the browser look more like a regular user
        stealth_sync(page)
        print("✅ Stealth patches applied to bypass bot detection.")

        try:
            # Step 1: Set the store context. This is essential.
            if not set_store_context(page, STORE_URL):
                print("❌ Failed to set store context. Exiting.")
                return

            # Step 2: Loop through all search queries
            for query in SEARCH_QUERIES:
                products = scrape_products_from_search(page, query)
                
                # Add unique products to our main list
                new_products_found = 0
                for product in products:
                    if product['ean'] not in scraped_eans:
                        all_products.append(product)
                        scraped_eans.add(product['ean'])
                        new_products_found += 1
                
                print(f"   -> Added {new_products_found} new unique products.")
                # A short delay between searches is good practice
                time.sleep(2)

        finally:
            print("\nClosing browser.")
            browser.close()

    # Step 3: Save the results to a file
    if all_products:
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            json.dump(all_products, f, ensure_ascii=False, indent=4)
        print(f"\n🎉 Scraping complete. Found {len(all_products)} unique products.")
        print(f"💾 Data saved to {OUTPUT_FILENAME}")
    else:
        print("\nNo products were scraped.")

if __name__ == "__main__":
    main()