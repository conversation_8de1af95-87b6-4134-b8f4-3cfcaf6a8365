import time
import json
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
# This is the correct import for modern versions of the library
from playwright_stealth import stealth_sync

def set_store_context(page, store_url):
    """
    Navigates to the store page and clicks the 'Faire mes courses' button
    to set the session cookies for the correct store.
    """
    print(f"Navigating to store page: {store_url}")
    try:
        # Go to the page and wait until the DOM is loaded
        page.goto(store_url, wait_until="domcontentloaded", timeout=60000)
        
        # Define the locator for the button. Playwright's locators auto-wait.
        shopping_button_locator = page.locator("//button[.//div[contains(text(), 'Faire mes courses')]]")
        
        print("Found 'Faire mes courses' button. Clicking...")
        # Click the button, waiting up to 20 seconds for it to be ready
        shopping_button_locator.click(timeout=20000)
        
        # Wait for the URL to change, indicating a successful navigation
        page.wait_for_url("**/accueil", timeout=20000)
        print("Successfully set store context.")
        time.sleep(3) # Give the page a moment to settle
        return True
    except PlaywrightTimeoutError:
        print("Error: Could not find or click the 'Faire mes courses' button or the page did not redirect in time.")
        return False
    except Exception as e:
        print(f"An unexpected error occurred while setting store context: {e}")
        return False

def scroll_to_bottom(page):
    """Scrolls to the bottom of the page to trigger dynamic content loading."""
    print("Scrolling to load all products...")
    last_height = page.evaluate("document.body.scrollHeight")
    while True:
        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        try:
            # Wait for the network to be idle, which is a good sign that new content has loaded
            page.wait_for_load_state("networkidle", timeout=5000)
        except PlaywrightTimeoutError:
            # If the network doesn't become idle (e.g., persistent connections), we'll break after a short wait
            print("Network idle timeout, continuing scroll check...")
            time.sleep(2)

        new_height = page.evaluate("document.body.scrollHeight")
        if new_height == last_height:
            print("Reached the bottom of the page.")
            break
        last_height = new_height

def scrape_products_from_search(page, search_term):
    """Navigates to a search results page, scrolls, and scrapes product data."""
    search_url = f"https://www.intermarche.com/recherche/{search_term}"
    print(f"\nSearching for: '{search_term}'")
    
    try:
        page.goto(search_url, wait_until="domcontentloaded", timeout=60000)
        # Wait for the product grid to appear
        page.wait_for_selector(".stime-product-list__grid", timeout=20000)
    except PlaywrightTimeoutError:
        print(f"No results found or page timed out for '{search_term}'.")
        return []

    scroll_to_bottom(page)

    products_data = []
    # Locate all product tiles. Playwright's locators are powerful.
    product_tiles = page.locator(".stime-product-list__item").all()
    print(f"Found {len(product_tiles)} product tiles for '{search_term}'.")

    for tile in product_tiles:
        try:
            # The EAN is in the href of the product link
            link_element = tile.locator(".productCard__link")
            href = link_element.get_attribute('href')
            if not href:
                continue
            
            ean = href.split('/')[-1]
            
            # Basic validation to ensure it looks like an EAN
            if ean.isdigit() and (len(ean) == 13 or len(ean) == 8):
                name_element = tile.locator(".stime-product--details__title")
                name = name_element.inner_text().strip()
                
                products_data.append({
                    "name": name,
                    "ean": ean,
                    "search_term": search_term
                })
        except Exception as e:
            # This tile might be an ad or something else. Log and skip.
            # print(f"Could not process a tile, skipping. Error: {e}")
            continue
            
    return products_data

def main():
    # --- Configuration ---
    STORE_URL = "https://www.intermarche.com/magasins/11621/villeneuve-loubet-06270/infos-pratiques"
    SEARCH_QUERIES = ["lait", "beurre", "pain", "eau", "fromage", "yaourt", "jambon", "pates", "riz", "chocolat", "cafe", "sucre", "farine", "huile"]
    OUTPUT_FILENAME = "intermarche_products_playwright.json"
    
    all_products = []
    scraped_eans = set()

    # The 'with' statement ensures Playwright resources are cleaned up properly
    with sync_playwright() as p:
        # Launch a browser. headless=False makes it visible.
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        context = browser.new_context(no_viewport=True)
        page = context.new_page()
        
        # This is the correct function call for modern versions
        stealth_sync(page)
        print("Stealth patches applied to bypass bot detection.")

        try:
            # Step 1: Set the store context. This is essential.
            if not set_store_context(page, STORE_URL):
                print("Failed to set store context. Exiting.")
                return

            # Step 2: Loop through all search queries
            for query in SEARCH_QUERIES:
                products = scrape_products_from_search(page, query)
                
                # Add unique products to our main list
                for product in products:
                    if product['ean'] not in scraped_eans:
                        all_products.append(product)
                        scraped_eans.add(product['ean'])
                
                # A short delay between searches is good practice
                time.sleep(2)

        finally:
            print("\nClosing browser.")
            browser.close()

    # Step 3: Save the results to a file
    if all_products:
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            json.dump(all_products, f, ensure_ascii=False, indent=4)
        print(f"\nScraping complete. Found {len(all_products)} unique products.")
        print(f"Data saved to {OUTPUT_FILENAME}")
    else:
        print("\nNo products were scraped.")

if __name__ == "__main__":
    main()