import asyncio
import random
from playwright.async_api import async_playwright

# --- ⚙️ Configuration ---

# 1. Add the search terms you want to scrape here.
SEARCH_TERMS = [
    'lait', 'oeuf', 'beurre', 'fromage', 'yaourt', 'jambon', 'pates', 
    'riz', 'chocolat', 'cafe', 'biscottes', 'confiture', 'eau', 'soda'
]

# 2. Name of the file where the EANs will be saved.
OUTPUT_FILE = "intermarche_eans.txt"

# 3. Set to True to run without a visible browser window.
#    Set to False to watch the scraper work.
HEADLESS_MODE = True

# --- End of Configuration ---


async def get_eans_from_page(page):
    """Extracts all EANs from the product links on the current page."""
    eans = set()
    # The EAN is the numeric part at the end of the product URL.
    product_links = page.locator("a.productCard__link")
    
    count = await product_links.count()
    if count == 0:
        return set()

    for i in range(count):
        link = product_links.nth(i)
        href = await link.get_attribute("href")
        if href:
            try:
                # URL format is typically /produit/product-name/3533630097654
                # We split by '/' and take the last segment.
                ean = href.strip().split('/')[-1]
                # Basic validation: ensure it's a number and has a reasonable length.
                if ean.isdigit() and len(ean) > 10:
                    eans.add(ean)
            except (IndexError, ValueError):
                # Ignore if the URL format is unexpected.
                continue
    return eans

async def main():
    """Main function to orchestrate the scraping process."""
    print("🚀 Starting Intermarché EAN scraper...")
    all_found_eans = set()

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=HEADLESS_MODE)
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        )
        page = await context.new_page()
        
        # Apply basic stealth measures to make the browser harder to detect.
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)

        # First, visit the homepage to handle any cookie consent pop-ups.
        print("🌍 Navigating to homepage to handle cookie consent...")
        try:
            await page.goto("https://www.intermarche.com", wait_until="domcontentloaded", timeout=60000)
            cookie_button_selector = "#didomi-notice-agree-button"
            try:
                await page.wait_for_selector(cookie_button_selector, timeout=10000)
                print("🍪 Cookie consent button found. Clicking 'Agree'.")
                await page.click(cookie_button_selector)
                await page.wait_for_timeout(2000) # Wait for banner to disappear.
            except:
                print("✅ No cookie consent button found, proceeding.")
        except Exception as e:
            print(f"⚠️ Could not handle cookie consent, but continuing. Error: {e}")

        for term in SEARCH_TERMS:
            print(f"\n🔍 Searching for term: '{term}'")
            page_number = 1
            term_eans = set()
            
            while True:
                search_url = f"https://www.intermarche.com/recherche/{term}?page={page_number}"
                print(f"   📄 Scraping page {page_number} for '{term}'...")
                
                try:
                    await page.goto(search_url, wait_until="networkidle", timeout=60000)
                    # Wait for the product grid to ensure products are loaded.
                    await page.wait_for_selector(".stime-product-list__grid", timeout=20000)
                except Exception:
                    print(f"   ❌ Page {page_number} for '{term}' did not load products or does not exist. Moving to next term.")
                    break

                eans_on_page = await get_eans_from_page(page)

                if not eans_on_page:
                    print(f"   🏁 No more products found for '{term}'. Moving to the next term.")
                    break

                new_eans_count = len(eans_on_page - term_eans)
                if new_eans_count == 0 and page_number > 1:
                    print(f"   🏁 Reached a duplicate or empty page. No new products found for '{term}'.")
                    break
                
                term_eans.update(eans_on_page)
                all_found_eans.update(eans_on_page)
                
                print(f"   ✅ Found {len(eans_on_page)} products. Total unique EANs for '{term}': {len(term_eans)}.")
                
                page_number += 1
                # Add a polite random delay to mimic human behavior.
                await asyncio.sleep(random.uniform(2, 5))

        await browser.close()

    print(f"\n\n🎉 Scraping finished!")
    print(f"Total unique EANs found across all terms: {len(all_found_eans)}")

    # Save the results to the output file.
    if all_found_eans:
        with open(OUTPUT_FILE, "w") as f:
            for ean in sorted(list(all_found_eans)):
                f.write(f"{ean}\n")
        print(f"💾 Results saved to {OUTPUT_FILE}")
    else:
        print("No EANs were found. The website structure may have changed.")

if __name__ == "__main__":
    asyncio.run(main())