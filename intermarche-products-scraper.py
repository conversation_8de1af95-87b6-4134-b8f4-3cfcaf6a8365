import time
import json
import os
from datetime import datetime
from camoufox.sync_api import Camoufox
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError

def save_html_log(content, filename_suffix):
    """Save HTML content to a log file for debugging"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"html_log_{timestamp}_{filename_suffix}.html"

    os.makedirs("html_logs", exist_ok=True)
    filepath = os.path.join("html_logs", filename)

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"📄 HTML saved to: {filepath}")
    return filepath

def handle_captcha(page):
    """Checks for a CAPTCHA and waits for the user to solve it."""
    print("🛡️ Checking for CAPTCHA...")
    # Selectors for common CAPTCHA providers like hCaptcha or reCAPTCHA
    captcha_selectors = [
        "iframe[title*='hCaptcha']",
        "iframe[title*='reCAPTCHA']",
        "iframe[src*='captcha']",
        "div:has-text('Vérifiez que vous')",
        "div:has-text('robot')",
        ".captcha",
        "#captcha"
    ]
    
    for selector in captcha_selectors:
        try:
            # Wait for a short period to see if the CAPTCHA appears
            captcha_frame = page.locator(selector).first
            try:
                captcha_frame.wait_for(state='visible', timeout=5000)
                if captcha_frame.is_visible():
                    print(f"🛡️ CAPTCHA detected with selector: {selector}")
                    print("✋ Please solve the CAPTCHA in the browser window.")
                    print("   The script will automatically resume once the CAPTCHA is solved.")

                    # Wait indefinitely (timeout=0) for the CAPTCHA to disappear
                    captcha_frame.wait_for(state='hidden', timeout=0)

                    print("✅ CAPTCHA solved. Resuming...")
                    time.sleep(3)  # Wait a bit for the page to reload/settle
                    return True # Exit after handling the first found CAPTCHA
            except PlaywrightTimeoutError:
                # CAPTCHA not visible, continue to next selector
                continue
        except PlaywrightTimeoutError:
            # This is expected if the CAPTCHA doesn't exist for this selector
            continue
    
    print("ℹ️ No CAPTCHA detected.")
    return False

def handle_cookie_consent(page):
    """Handle cookie consent popup (usually from Didomi)"""
    print("🍪 Checking for cookie consent popup...")

    # Give the banner a moment to appear after page load or a CAPTCHA solve
    time.sleep(3)
    save_html_log(page.content(), "before_cookie_consent")

    # Check for Didomi popup specifically first
    didomi_selectors = [
        ".didomi-continue-without-agreeing",  # "Continuer sans accepter" button
        "#didomi-notice-agree-button",        # Primary accept button
        "button:has-text('Continuer sans accepter')",
        "span:has-text('Continuer sans accepter')",
        "[role='button']:has-text('Continuer sans accepter')"
    ]

    print("🍪 Checking for Didomi popup...")

    # First try to remove the popup with JavaScript
    try:
        print("🍪 Trying to remove Didomi popup with JavaScript...")
        page.evaluate("""
            // Remove Didomi popup elements
            const didomiHost = document.querySelector('#didomi-host');
            if (didomiHost) {
                didomiHost.remove();
                console.log('Removed didomi-host');
            }

            const didomiPopup = document.querySelector('.didomi-popup-view');
            if (didomiPopup) {
                didomiPopup.remove();
                console.log('Removed didomi-popup-view');
            }

            // Remove any overlay
            const overlay = document.querySelector('.didomi-popup-backdrop');
            if (overlay) {
                overlay.remove();
                console.log('Removed didomi-popup-backdrop');
            }

            // Set body overflow back to visible
            document.body.style.overflow = 'visible';
        """)
        time.sleep(2)
        print("✅ Didomi popup removed with JavaScript.")
        save_html_log(page.content(), "after_didomi_js_removal")
        return True
    except Exception as e:
        print(f"⚠️ JavaScript removal failed: {e}")

    # Fallback to clicking buttons
    for selector in didomi_selectors:
        try:
            element = page.locator(selector).first
            if element.is_visible():
                print(f"🍪 Found Didomi element: {selector}")
                # Try JavaScript click first
                try:
                    page.evaluate(f"document.querySelector('{selector}').click()")
                    print("✅ Didomi popup handled with JavaScript click.")
                    time.sleep(3)
                    save_html_log(page.content(), "after_didomi_consent")
                    return True
                except:
                    # Fallback to Playwright click
                    element.scroll_into_view_if_needed()
                    time.sleep(1)
                    element.click(force=True, timeout=10000)
                    print("✅ Didomi popup handled with Playwright click.")
                    time.sleep(3)
                    save_html_log(page.content(), "after_didomi_consent")
                    return True
        except Exception as e:
            print(f"⚠️ Failed to handle Didomi element {selector}: {e}")
            continue

    # Fallback to other cookie consent selectors
    other_selectors = [
        "button:has-text('Tout accepter')",
        "button:has-text('Accepter et fermer')",
        "[data-testid='agree-button']",
        "button[aria-label*='Accepter']",
        "button[aria-label*='Accept']",
        "button:has-text('Accepter')",
        "button:has-text('Accept')",
        "#onetrust-accept-btn-handler",
    ]

    print("🍪 Checking for other cookie consent popups...")
    for selector in other_selectors:
        try:
            button = page.locator(selector).first
            if button.is_visible():
                print(f"🍪 Found cookie button: {selector}")
                button.scroll_into_view_if_needed()
                time.sleep(1)
                button.click(force=True, timeout=10000)
                print("✅ Cookie consent accepted.")
                time.sleep(3)
                save_html_log(page.content(), "after_cookie_consent")
                return True
        except Exception as e:
            print(f"⚠️ Failed to click cookie button {selector}: {e}")
            continue

    print("ℹ️ No cookie consent popup found or couldn't handle it.")
    save_html_log(page.content(), "no_cookie_consent_found")
    return False

def set_store_context(page, store_url):
    """
    Navigates to the store page, handles CAPTCHA and cookies, and then
    clicks the 'Faire mes courses' button to set the session.
    """
    print(f"Navigating to store page: {store_url}")
    try:
        # Go to the page and wait until the DOM is loaded
        page.goto(store_url, wait_until="domcontentloaded", timeout=60000)
        save_html_log(page.content(), "store_page_initial")

        # Step 1: Handle potential CAPTCHA first, as it blocks everything else
        handle_captcha(page)

        # Step 2: Handle cookie consent after the CAPTCHA is gone - try multiple times
        cookie_handled = False
        for attempt in range(5):  # Try up to 5 times
            print(f"🍪 Cookie consent attempt {attempt + 1}/5")
            if handle_cookie_consent(page):
                cookie_handled = True
                break
            time.sleep(2)

        if not cookie_handled:
            print("⚠️ Could not handle cookie consent after 5 attempts, trying to dismiss with Escape")
            page.keyboard.press('Escape')
            time.sleep(2)
            # Try one more time after Escape
            handle_cookie_consent(page)

        # Wait a moment for any overlays to disappear
        time.sleep(3)
        save_html_log(page.content(), "store_page_after_popups")

        # Define the locator for the button. Playwright's locators auto-wait.
        shopping_button_locator = page.locator("button:has-text('Faire mes courses')")

        # Check if button is visible
        try:
            shopping_button_locator.first.wait_for(state='visible', timeout=10000)
            button_found = shopping_button_locator.first.is_visible()
        except PlaywrightTimeoutError:
            button_found = False

        if not button_found:
            print("⚠️ 'Faire mes courses' button not found, trying alternatives...")
            alternative_selectors = [
                "//button[.//div[contains(text(), 'Faire mes courses')]]",
                "[data-testid*='shopping']",
                "button[aria-label*='courses']",
                "a:has-text('Faire mes courses')"
            ]
            for selector in alternative_selectors:
                try:
                    alt_button = page.locator(selector).first
                    alt_button.wait_for(state='visible', timeout=2000)
                    if alt_button.is_visible():
                        print(f"✅ Found button with alternative selector: {selector}")
                        shopping_button_locator = alt_button
                        button_found = True
                        break
                except PlaywrightTimeoutError:
                    continue

            if not button_found:
                # Check if this store doesn't support drive/online shopping
                no_drive_indicators = [
                    "text=Ce magasin ne propose pas de service drive",
                    "text=Service drive non disponible",
                    "text=Pas de drive",
                    "text=Drive non disponible"
                ]

                for indicator in no_drive_indicators:
                    try:
                        if page.locator(indicator).is_visible():
                            print("⚠️ This store doesn't support drive/online shopping. Skipping...")
                            return False
                    except:
                        continue

                print("❌ Could not find 'Faire mes courses' button with any selector.")
                save_html_log(page.content(), "store_page_no_button_found")
                return False

        print("✅ Found 'Faire mes courses' button. Attempting to click...")

        # Before clicking, try one more time to dismiss any overlays
        try:
            # Check if Didomi popup is still there and try to dismiss it
            didomi_popup = page.locator(".didomi-popup-view").first
            if didomi_popup.is_visible():
                print("🍪 Didomi popup still visible, trying to dismiss...")
                continue_button = page.locator(".didomi-continue-without-agreeing").first
                if continue_button.is_visible():
                    continue_button.click(force=True)
                    time.sleep(2)
        except:
            pass

        # Try multiple click strategies
        click_success = False

        # Strategy 1: Simple click
        try:
            print("🔄 Trying simple click...")
            shopping_button_locator.click(timeout=10000)
            click_success = True
            print("✅ Simple click succeeded")
        except Exception as e:
            print(f"⚠️ Simple click failed: {e}")

        # Strategy 2: Force click if simple click failed
        if not click_success:
            try:
                print("🔄 Trying force click...")
                shopping_button_locator.click(force=True, timeout=10000)
                click_success = True
                print("✅ Force click succeeded")
            except Exception as e:
                print(f"⚠️ Force click failed: {e}")

        # Strategy 3: JavaScript click if force click failed
        if not click_success:
            try:
                print("🔄 Trying JavaScript click...")
                page.evaluate("document.querySelector(\"button[aria-label='Faire mes courses']\").click()")
                click_success = True
                print("✅ JavaScript click succeeded")
            except Exception as e:
                print(f"⚠️ JavaScript click failed: {e}")

        if not click_success:
            print("❌ All click strategies failed")
            return False

        # Wait for the URL to change, indicating a successful navigation
        try:
            page.wait_for_url("**/accueil", timeout=15000)  # Reduced timeout
            print("✅ Successfully navigated to shopping page.")
        except PlaywrightTimeoutError:
            print("⚠️ URL didn't change to accueil, but continuing anyway...")

        save_html_log(page.content(), "store_context_success")
        print("✅ Store context setup completed.")
        time.sleep(2)  # Brief pause before continuing
        return True

    except PlaywrightTimeoutError as e:
        print(f"❌ Error: A timeout occurred while setting the store context. {e}")
        save_html_log(page.content(), "store_context_timeout_error")
        return False
    except Exception as e:
        print(f"❌ An unexpected error occurred while setting store context: {e}")
        save_html_log(page.content(), "store_context_unexpected_error")
        return False

def scroll_to_bottom(page):
    """Scrolls to the bottom of the page to trigger dynamic content loading."""
    print("📜 Scrolling to load all products...")
    last_height = page.evaluate("document.body.scrollHeight")
    while True:
        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        try:
            # Wait for the network to be idle, a good sign that new content has loaded
            page.wait_for_load_state("networkidle", timeout=5000)
        except PlaywrightTimeoutError:
            # If the network doesn't become idle, we'll break after a short wait
            print("   - Network idle timeout, continuing scroll check...")
            time.sleep(2)

        new_height = page.evaluate("document.body.scrollHeight")
        if new_height == last_height:
            print("✅ Reached the bottom of the page.")
            break
        last_height = new_height

def scrape_eans_from_search_with_pagination(page, search_term):
    """Navigate to search URL and scrape ALL EAN codes using pagination."""
    print(f"\n🔎 Searching for: '{search_term}' using pagination")

    all_eans = set()
    page_num = 1

    while True:
        # Construct URL for current page
        if page_num == 1:
            search_url = f"https://www.intermarche.com/recherche/{search_term}"
        else:
            search_url = f"https://www.intermarche.com/recherche/{search_term}?page={page_num}"

        print(f"📄 Scraping page {page_num}: {search_url}")

        try:
            # Navigate to the page
            time.sleep(1)  # Brief pause between pages
            page.goto(search_url, wait_until="domcontentloaded", timeout=30000)
            time.sleep(1)

            # Wait for the product grid to appear
            try:
                page.wait_for_selector(".stime-product-list__grid", timeout=10000)
            except:
                print(f"   ⚠️ No product grid found on page {page_num}")
                break

            # Extract EANs from this page
            product_tiles = page.locator(".stime-product-list__item").all()
            page_eans = set()

            for tile in product_tiles:
                try:
                    # The EAN is in the href of the product link
                    link_element = tile.locator(".productCard__link")
                    href = link_element.get_attribute('href')
                    if not href:
                        continue

                    ean = href.split('/')[-1]

                    # Basic validation to ensure it looks like an EAN
                    if ean.isdigit() and (len(ean) == 13 or len(ean) == 8):
                        page_eans.add(ean)

                except Exception:
                    # Skip problematic tiles
                    continue

            print(f"   → Found {len(page_eans)} EANs on page {page_num}")

            # Add EANs to our collection
            all_eans.update(page_eans)
            print(f"   → Total EANs collected so far: {len(all_eans)}")

            # If no EANs found on this page, check if it's really the end
            if len(page_eans) == 0:
                print(f"   → No products found on page {page_num}")

                # Check if this is a "no results" page or just an empty page
                # Look for pagination indicators or "no results" messages
                try:
                    # Check for "no results" or "end of results" indicators
                    no_results_indicators = [
                        "text=Aucun résultat",
                        "text=Aucun produit",
                        "text=Pas de résultat",
                        ".no-results",
                        ".empty-results"
                    ]

                    is_no_results_page = False
                    for indicator in no_results_indicators:
                        try:
                            if page.locator(indicator).count() > 0:
                                is_no_results_page = True
                                break
                        except:
                            continue

                    # Also check if we can see pagination that suggests more pages
                    has_pagination = False
                    try:
                        # Look for pagination elements
                        pagination_selectors = [
                            ".pagination",
                            "[aria-label*='pagination']",
                            "a[href*='page=']"
                        ]
                        for selector in pagination_selectors:
                            if page.locator(selector).count() > 0:
                                has_pagination = True
                                break
                    except:
                        pass

                    if is_no_results_page:
                        print(f"   → Confirmed no results page, stopping")
                        break
                    elif not has_pagination and page_num > 1:
                        print(f"   → No pagination found and no products, likely end of results")
                        break
                    else:
                        print(f"   → Empty page but might have more, trying a few more pages...")
                        # Try a few more pages in case this is just an empty page
                        if page_num > 50:  # Don't go too far
                            print(f"   → Reached reasonable limit, stopping")
                            break
                except Exception as e:
                    print(f"   → Error checking page indicators: {e}")
                    break

            page_num += 1

            # Safety limit to prevent infinite loops
            if page_num > 200:  # Increased safety limit
                print(f"   → Reached safety limit of 200 pages")
                break

        except Exception as e:
            print(f"❌ Error on page {page_num}: {e}")

            # Don't give up immediately on errors - might be temporary
            if "Page crashed" in str(e) or "timeout" in str(e).lower():
                print(f"   → Page crash/timeout on page {page_num}, trying to continue...")
                # Wait a moment for recovery
                time.sleep(3)
                # Try a few more pages in case this was just a temporary issue
                if page_num < 50:  # Don't go too far on errors
                    page_num += 1
                    continue
                else:
                    print(f"   → Too many pages tried, stopping")
                    break
            else:
                # Other types of errors, stop
                break

    print(f"✅ Total EANs collected for '{search_term}': {len(all_eans)} across {page_num-1} pages")
    return list(all_eans)

def main():
    # --- Configuration ---
    STORE_URL = "https://www.intermarche.com/magasins/11621/villeneuve-loubet-06270/infos-pratiques"

    # Restart from "sucre" after connection cut - remaining search terms
    SEARCH_QUERIES = [
        # Pantry Staples (continuing from sucre)
        "sucre", "huile", "vinaigre", "sel",

        # Beverages
        "eau", "jus", "soda", "bière", "vin", "café", "thé",

        # Snacks & Sweets
        "chocolat", "bonbon", "chips", "cacahuète", "biscuit apéritif",

        # Household & Personal Care
        "lessive", "savon", "shampoing", "dentifrice", "papier toilette",

        # Baby & Health
        "couche", "biberon", "médicament", "vitamine",

        # Frozen Foods
        "surgelé", "glace", "pizza surgelée"
    ]

    OUTPUT_FILENAME = "intermarche_comprehensive_eans.json"

    # Load existing EANs from previous progress (before connection cut at "sucre")
    all_eans = set()
    try:
        # Load progress file from before "sucre" (27 terms completed, at "riz")
        with open("progress_eans_8494_terms.json", 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
            existing_eans = progress_data.get('eans', [])
            all_eans = set(existing_eans)
            print(f"🔄 Loaded {len(all_eans)} existing EANs from previous session")
            print(f"📊 Previous progress: {progress_data.get('progress', 'Unknown')}")
            print(f"📊 Last completed: {progress_data.get('current_search', 'Unknown')}")
            print(f"🚀 Continuing from 'sucre' onwards...")
    except Exception as e:
        print(f"⚠️ Could not load previous progress: {e}")
        print(f"🆕 Starting fresh collection...")
        all_eans = set()

    with Camoufox(
        headless=False,  # Keep visible for manual CAPTCHA solving
        humanize=True,   # Enable human-like cursor movement
        os="windows",    # Match the current OS
        geoip=True,      # Auto-detect IP for geolocation matching
        block_images=False,  # Keep images for full page rendering
        disable_coop=True,   # Allow cross-origin iframe interactions
        i_know_what_im_doing=True  # Acknowledge COOP warning
    ) as browser:
        # Create context with location permissions
        context = browser.new_context(
            permissions=['geolocation'],
            geolocation={'latitude': 43.6532, 'longitude': 7.1378}  # Villeneuve-Loubet coordinates
        )
        page = context.new_page()
        print("✅ Camoufox browser launched with anti-detection features and location permissions.")

        try:
            # Step 1: Set the store context. This is essential.
            if not set_store_context(page, STORE_URL):
                print("❌ Failed to set store context. Exiting.")
                return

            # Step 2: Loop through remaining search queries (starting from "sucre")
            print(f"\n🚀 Resuming comprehensive EAN collection with {len(SEARCH_QUERIES)} remaining search terms")
            print(f"🔄 Starting from 'sucre' after connection cut")
            print("=" * 80)

            # Start numbering from where we left off (around term 30)
            starting_term_number = 30
            for i, query in enumerate(SEARCH_QUERIES, starting_term_number):
                print(f"\n[{i}/57] Processing search term: '{query}'")
                print("-" * 50)

                try:
                    eans = scrape_eans_from_search_with_pagination(page, query)

                    # Add unique EANs to our collection
                    new_eans_found = 0
                    for ean in eans:
                        if ean not in all_eans:
                            all_eans.add(ean)
                            new_eans_found += 1

                    print(f"   ✅ '{query}': Found {len(eans)} EANs, {new_eans_found} new unique")
                    print(f"   📊 Total unique EANs so far: {len(all_eans)}")

                    # Save progress after each search term
                    progress_filename = f"progress_eans_{len(all_eans)}_terms.json"
                    eans_list = sorted(list(all_eans))

                    # Build list of all completed searches (previous + current)
                    previous_searches = ["lait", "beurre", "fromage", "yaourt", "crème", "oeufs", "pain", "brioche", "croissant", "biscuit", "gâteau", "viande", "porc", "boeuf", "poulet", "jambon", "saucisse", "poisson", "saumon", "pomme", "banane", "orange", "tomate", "carotte", "pomme de terre", "salade", "riz", "pâtes", "farine"]
                    current_batch_completed = SEARCH_QUERIES[:i-starting_term_number+1]
                    all_completed = previous_searches + current_batch_completed

                    progress_data = {
                        "completed_searches": all_completed,
                        "current_search": query,
                        "total_eans": len(eans_list),
                        "eans": eans_list,
                        "progress": f"{i}/57 search terms completed"
                    }

                    with open(progress_filename, 'w', encoding='utf-8') as f:
                        json.dump(progress_data, f, ensure_ascii=False, indent=2)
                    print(f"   💾 Progress saved to: {progress_filename}")

                except Exception as e:
                    print(f"   ❌ Error processing '{query}': {e}")
                    print(f"   ⏭️ Continuing with next search term...")
                    continue

                # Brief delay between searches
                time.sleep(1)  # Small delay to be respectful

        finally:
            print("\nClosing browser.")
            browser.close()

    # Step 3: Save the final comprehensive results
    print("\n" + "=" * 80)
    print("🎉 COMPREHENSIVE EAN COLLECTION COMPLETE!")
    print("=" * 80)

    if all_eans:
        eans_list = sorted(list(all_eans))

        # Create comprehensive results
        results = {
            "collection_date": datetime.now().isoformat(),
            "store_info": {
                "store_id": "11621",
                "store_name": "Villeneuve-Loubet",
                "store_url": STORE_URL
            },
            "search_queries": SEARCH_QUERIES,
            "total_search_terms": len(SEARCH_QUERIES),
            "total_unique_eans": len(eans_list),
            "average_eans_per_term": len(eans_list) / len(SEARCH_QUERIES),
            "eans": eans_list
        }

        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # Display final statistics
        print(f"📊 FINAL STATISTICS:")
        print(f"   🔍 Search terms processed: {len(SEARCH_QUERIES)}")
        print(f"   📦 Total unique EANs collected: {len(eans_list):,}")
        print(f"   📈 Average EANs per search term: {len(eans_list) / len(SEARCH_QUERIES):.1f}")
        print(f"   🏪 Store: Villeneuve-Loubet (ID: 11621)")
        print(f"   📁 Results saved to: {OUTPUT_FILENAME}")

        # Also save as simple text file for easy viewing
        txt_filename = OUTPUT_FILENAME.replace('.json', '.txt')
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(f"# Intermarché EAN Collection Results\n")
            f.write(f"# Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Total EANs: {len(eans_list):,}\n")
            f.write(f"# Search Terms: {len(SEARCH_QUERIES)}\n")
            f.write(f"# Store: Villeneuve-Loubet (11621)\n\n")
            for ean in eans_list:
                f.write(f"{ean}\n")
        print(f"   📄 EANs also saved as text file: {txt_filename}")

        print(f"\n🚀 Ready for multi-store price comparison across 2,086 stores!")
        print(f"💡 Potential price points: {len(eans_list):,} EANs × 2,086 stores = {len(eans_list) * 2086:,} price comparisons")

    else:
        print("❌ No EANs were scraped.")

if __name__ == "__main__":
    main()