import time
import json
import os
from datetime import datetime
from camoufox.sync_api import Camoufox
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError

def save_html_log(content, filename_suffix):
    """Save HTML content to a log file for debugging"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"html_log_{timestamp}_{filename_suffix}.html"

    os.makedirs("html_logs", exist_ok=True)
    filepath = os.path.join("html_logs", filename)

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"📄 HTML saved to: {filepath}")
    return filepath

def handle_captcha(page):
    """Checks for a CAPTCHA and waits for the user to solve it."""
    print("🛡️ Checking for CAPTCHA...")
    # Selectors for common CAPTCHA providers like hCaptcha or reCAPTCHA
    captcha_selectors = [
        "iframe[title*='hCaptcha']",
        "iframe[title*='reCAPTCHA']",
        "iframe[src*='captcha']",
        "div:has-text('Vérifiez que vous')",
        "div:has-text('robot')",
        ".captcha",
        "#captcha"
    ]
    
    for selector in captcha_selectors:
        try:
            # Wait for a short period to see if the CAPTCHA appears
            captcha_frame = page.locator(selector).first
            try:
                captcha_frame.wait_for(state='visible', timeout=5000)
                if captcha_frame.is_visible():
                    print(f"🛡️ CAPTCHA detected with selector: {selector}")
                    print("✋ Please solve the CAPTCHA in the browser window.")
                    print("   The script will automatically resume once the CAPTCHA is solved.")

                    # Wait indefinitely (timeout=0) for the CAPTCHA to disappear
                    captcha_frame.wait_for(state='hidden', timeout=0)

                    print("✅ CAPTCHA solved. Resuming...")
                    time.sleep(3)  # Wait a bit for the page to reload/settle
                    return True # Exit after handling the first found CAPTCHA
            except PlaywrightTimeoutError:
                # CAPTCHA not visible, continue to next selector
                continue
        except PlaywrightTimeoutError:
            # This is expected if the CAPTCHA doesn't exist for this selector
            continue
    
    print("ℹ️ No CAPTCHA detected.")
    return False

def handle_cookie_consent(page):
    """Handle cookie consent popup (usually from Didomi)"""
    print("🍪 Checking for cookie consent popup...")

    # Give the banner a moment to appear after page load or a CAPTCHA solve
    time.sleep(3)
    save_html_log(page.content(), "before_cookie_consent")

    # Check for Didomi popup specifically first
    didomi_selectors = [
        ".didomi-continue-without-agreeing",  # "Continuer sans accepter" button
        "#didomi-notice-agree-button",        # Primary accept button
        "button:has-text('Continuer sans accepter')",
        "span:has-text('Continuer sans accepter')",
        "[role='button']:has-text('Continuer sans accepter')"
    ]

    print("🍪 Checking for Didomi popup...")

    # First try to remove the popup with JavaScript
    try:
        print("🍪 Trying to remove Didomi popup with JavaScript...")
        page.evaluate("""
            // Remove Didomi popup elements
            const didomiHost = document.querySelector('#didomi-host');
            if (didomiHost) {
                didomiHost.remove();
                console.log('Removed didomi-host');
            }

            const didomiPopup = document.querySelector('.didomi-popup-view');
            if (didomiPopup) {
                didomiPopup.remove();
                console.log('Removed didomi-popup-view');
            }

            // Remove any overlay
            const overlay = document.querySelector('.didomi-popup-backdrop');
            if (overlay) {
                overlay.remove();
                console.log('Removed didomi-popup-backdrop');
            }

            // Set body overflow back to visible
            document.body.style.overflow = 'visible';
        """)
        time.sleep(2)
        print("✅ Didomi popup removed with JavaScript.")
        save_html_log(page.content(), "after_didomi_js_removal")
        return True
    except Exception as e:
        print(f"⚠️ JavaScript removal failed: {e}")

    # Fallback to clicking buttons
    for selector in didomi_selectors:
        try:
            element = page.locator(selector).first
            if element.is_visible():
                print(f"🍪 Found Didomi element: {selector}")
                # Try JavaScript click first
                try:
                    page.evaluate(f"document.querySelector('{selector}').click()")
                    print("✅ Didomi popup handled with JavaScript click.")
                    time.sleep(3)
                    save_html_log(page.content(), "after_didomi_consent")
                    return True
                except:
                    # Fallback to Playwright click
                    element.scroll_into_view_if_needed()
                    time.sleep(1)
                    element.click(force=True, timeout=10000)
                    print("✅ Didomi popup handled with Playwright click.")
                    time.sleep(3)
                    save_html_log(page.content(), "after_didomi_consent")
                    return True
        except Exception as e:
            print(f"⚠️ Failed to handle Didomi element {selector}: {e}")
            continue

    # Fallback to other cookie consent selectors
    other_selectors = [
        "button:has-text('Tout accepter')",
        "button:has-text('Accepter et fermer')",
        "[data-testid='agree-button']",
        "button[aria-label*='Accepter']",
        "button[aria-label*='Accept']",
        "button:has-text('Accepter')",
        "button:has-text('Accept')",
        "#onetrust-accept-btn-handler",
    ]

    print("🍪 Checking for other cookie consent popups...")
    for selector in other_selectors:
        try:
            button = page.locator(selector).first
            if button.is_visible():
                print(f"🍪 Found cookie button: {selector}")
                button.scroll_into_view_if_needed()
                time.sleep(1)
                button.click(force=True, timeout=10000)
                print("✅ Cookie consent accepted.")
                time.sleep(3)
                save_html_log(page.content(), "after_cookie_consent")
                return True
        except Exception as e:
            print(f"⚠️ Failed to click cookie button {selector}: {e}")
            continue

    print("ℹ️ No cookie consent popup found or couldn't handle it.")
    save_html_log(page.content(), "no_cookie_consent_found")
    return False

def set_store_context(page, store_url):
    """
    Navigates to the store page, handles CAPTCHA and cookies, and then
    clicks the 'Faire mes courses' button to set the session.
    """
    print(f"Navigating to store page: {store_url}")
    try:
        # Go to the page and wait until the DOM is loaded
        page.goto(store_url, wait_until="domcontentloaded", timeout=60000)
        save_html_log(page.content(), "store_page_initial")

        # Step 1: Handle potential CAPTCHA first, as it blocks everything else
        handle_captcha(page)

        # Step 2: Handle cookie consent after the CAPTCHA is gone - try multiple times
        cookie_handled = False
        for attempt in range(5):  # Try up to 5 times
            print(f"🍪 Cookie consent attempt {attempt + 1}/5")
            if handle_cookie_consent(page):
                cookie_handled = True
                break
            time.sleep(2)

        if not cookie_handled:
            print("⚠️ Could not handle cookie consent after 5 attempts, trying to dismiss with Escape")
            page.keyboard.press('Escape')
            time.sleep(2)
            # Try one more time after Escape
            handle_cookie_consent(page)

        # Wait a moment for any overlays to disappear
        time.sleep(3)
        save_html_log(page.content(), "store_page_after_popups")

        # Define the locator for the button. Playwright's locators auto-wait.
        shopping_button_locator = page.locator("button:has-text('Faire mes courses')")

        # Check if button is visible
        try:
            shopping_button_locator.first.wait_for(state='visible', timeout=10000)
            button_found = shopping_button_locator.first.is_visible()
        except PlaywrightTimeoutError:
            button_found = False

        if not button_found:
            print("⚠️ 'Faire mes courses' button not found, trying alternatives...")
            alternative_selectors = [
                "//button[.//div[contains(text(), 'Faire mes courses')]]",
                "[data-testid*='shopping']",
                "button[aria-label*='courses']",
                "a:has-text('Faire mes courses')"
            ]
            for selector in alternative_selectors:
                try:
                    alt_button = page.locator(selector).first
                    alt_button.wait_for(state='visible', timeout=2000)
                    if alt_button.is_visible():
                        print(f"✅ Found button with alternative selector: {selector}")
                        shopping_button_locator = alt_button
                        button_found = True
                        break
                except PlaywrightTimeoutError:
                    continue

            if not button_found:
                # Check if this store doesn't support drive/online shopping
                no_drive_indicators = [
                    "text=Ce magasin ne propose pas de service drive",
                    "text=Service drive non disponible",
                    "text=Pas de drive",
                    "text=Drive non disponible"
                ]

                for indicator in no_drive_indicators:
                    try:
                        if page.locator(indicator).is_visible():
                            print("⚠️ This store doesn't support drive/online shopping. Skipping...")
                            return False
                    except:
                        continue

                print("❌ Could not find 'Faire mes courses' button with any selector.")
                save_html_log(page.content(), "store_page_no_button_found")
                return False

        print("✅ Found 'Faire mes courses' button. Attempting to click...")

        # Before clicking, try one more time to dismiss any overlays
        try:
            # Check if Didomi popup is still there and try to dismiss it
            didomi_popup = page.locator(".didomi-popup-view").first
            if didomi_popup.is_visible():
                print("🍪 Didomi popup still visible, trying to dismiss...")
                continue_button = page.locator(".didomi-continue-without-agreeing").first
                if continue_button.is_visible():
                    continue_button.click(force=True)
                    time.sleep(2)
        except:
            pass

        # Try multiple click strategies
        click_success = False

        # Strategy 1: Simple click
        try:
            print("🔄 Trying simple click...")
            shopping_button_locator.click(timeout=10000)
            click_success = True
            print("✅ Simple click succeeded")
        except Exception as e:
            print(f"⚠️ Simple click failed: {e}")

        # Strategy 2: Force click if simple click failed
        if not click_success:
            try:
                print("🔄 Trying force click...")
                shopping_button_locator.click(force=True, timeout=10000)
                click_success = True
                print("✅ Force click succeeded")
            except Exception as e:
                print(f"⚠️ Force click failed: {e}")

        # Strategy 3: JavaScript click if force click failed
        if not click_success:
            try:
                print("🔄 Trying JavaScript click...")
                page.evaluate("document.querySelector(\"button[aria-label='Faire mes courses']\").click()")
                click_success = True
                print("✅ JavaScript click succeeded")
            except Exception as e:
                print(f"⚠️ JavaScript click failed: {e}")

        if not click_success:
            print("❌ All click strategies failed")
            return False

        # Wait for the URL to change, indicating a successful navigation
        page.wait_for_url("**/accueil", timeout=30000)
        print("✅ Successfully set store context.")
        save_html_log(page.content(), "store_context_success")
        time.sleep(3) # Give the page a moment to settle
        return True

    except PlaywrightTimeoutError as e:
        print(f"❌ Error: A timeout occurred while setting the store context. {e}")
        save_html_log(page.content(), "store_context_timeout_error")
        return False
    except Exception as e:
        print(f"❌ An unexpected error occurred while setting store context: {e}")
        save_html_log(page.content(), "store_context_unexpected_error")
        return False

def scroll_to_bottom(page):
    """Scrolls to the bottom of the page to trigger dynamic content loading."""
    print("📜 Scrolling to load all products...")
    last_height = page.evaluate("document.body.scrollHeight")
    while True:
        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        try:
            # Wait for the network to be idle, a good sign that new content has loaded
            page.wait_for_load_state("networkidle", timeout=5000)
        except PlaywrightTimeoutError:
            # If the network doesn't become idle, we'll break after a short wait
            print("   - Network idle timeout, continuing scroll check...")
            time.sleep(2)

        new_height = page.evaluate("document.body.scrollHeight")
        if new_height == last_height:
            print("✅ Reached the bottom of the page.")
            break
        last_height = new_height

def scrape_products_from_search(page, search_term):
    """Navigates to a search results page, scrolls, and scrapes product data."""
    search_url = f"https://www.intermarche.com/recherche/{search_term}"
    print(f"\n🔎 Searching for: '{search_term}'")

    try:
        # Wait a moment before navigation to ensure page stability
        time.sleep(3)
        print(f"🌐 Navigating to: {search_url}")

        page.goto(search_url, wait_until="domcontentloaded", timeout=60000)

        # Wait for page to stabilize after navigation
        time.sleep(3)
        save_html_log(page.content(), f"search_{search_term}_initial")

        # Wait for the product grid to appear
        page.wait_for_selector(".stime-product-list__grid", timeout=20000)

    except PlaywrightTimeoutError:
        print(f"❌ No results found or page timed out for '{search_term}'.")
        save_html_log(page.content(), f"search_{search_term}_timeout_error")
        return []
    except Exception as e:
        print(f"❌ Error during navigation for '{search_term}': {e}")
        try:
            save_html_log(page.content(), f"search_{search_term}_error")
        except:
            pass
        return []

    scroll_to_bottom(page)

    products_data = []
    # Locate all product tiles.
    product_tiles = page.locator(".stime-product-list__item").all()
    print(f"Found {len(product_tiles)} product tiles for '{search_term}'.")

    for tile in product_tiles:
        try:
            # The EAN is in the href of the product link
            link_element = tile.locator(".productCard__link")
            href = link_element.get_attribute('href')
            if not href:
                continue
            
            ean = href.split('/')[-1]
            
            # Basic validation to ensure it looks like an EAN
            if ean.isdigit() and (len(ean) == 13 or len(ean) == 8):
                name_element = tile.locator(".stime-product--details__title")
                name = name_element.inner_text().strip()
                
                products_data.append({
                    "name": name,
                    "ean": ean,
                    "search_term": search_term
                })
        except Exception:
            # This tile might be an ad or something else. Skip silently.
            continue
            
    return products_data

def main():
    # --- Configuration ---
    STORE_URL = "https://www.intermarche.com/magasins/11621/villeneuve-loubet-06270/infos-pratiques"
    SEARCH_QUERIES = ["lait", "beurre"]  # Reduced for testing
    OUTPUT_FILENAME = "intermarche_products_playwright.json"
    
    all_products = []
    scraped_eans = set()

    with Camoufox(
        headless=False,  # Keep visible for manual CAPTCHA solving
        humanize=True,   # Enable human-like cursor movement
        os="windows",    # Match the current OS
        geoip=True,      # Auto-detect IP for geolocation matching
        block_images=False,  # Keep images for full page rendering
        disable_coop=True,   # Allow cross-origin iframe interactions
        i_know_what_im_doing=True  # Acknowledge COOP warning
    ) as browser:
        # Create context with location permissions
        context = browser.new_context(
            permissions=['geolocation'],
            geolocation={'latitude': 43.6532, 'longitude': 7.1378}  # Villeneuve-Loubet coordinates
        )
        page = context.new_page()
        print("✅ Camoufox browser launched with anti-detection features and location permissions.")

        try:
            # Step 1: Set the store context. This is essential.
            if not set_store_context(page, STORE_URL):
                print("❌ Failed to set store context. Exiting.")
                return

            # Step 2: Loop through all search queries
            for query in SEARCH_QUERIES:
                products = scrape_products_from_search(page, query)
                
                # Add unique products to our main list
                new_products_found = 0
                for product in products:
                    if product['ean'] not in scraped_eans:
                        all_products.append(product)
                        scraped_eans.add(product['ean'])
                        new_products_found += 1
                
                print(f"   -> Added {new_products_found} new unique products.")
                # A short delay between searches is good practice
                time.sleep(2)

        finally:
            print("\nClosing browser.")
            browser.close()

    # Step 3: Save the results to a file
    if all_products:
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            json.dump(all_products, f, ensure_ascii=False, indent=4)
        print(f"\n🎉 Scraping complete. Found {len(all_products)} unique products.")
        print(f"💾 Data saved to {OUTPUT_FILENAME}")
    else:
        print("\nNo products were scraped.")

if __name__ == "__main__":
    main()