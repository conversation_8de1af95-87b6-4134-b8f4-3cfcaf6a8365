#!/usr/bin/env python3
"""
Comprehensive EAN-based API test to maximize product data from working store
"""

import requests
import json
import time
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

class ComprehensiveEANTester:
    def __init__(self, store_id="12118", store_name="Super Marseille", store_city="marseille-13004"):
        self.base_url = "https://www.intermarche.com"
        self.session = requests.Session()

        # Headers from successful HAR requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'fr-FR,fr;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }

        # Store parameters (now configurable)
        self.store_id = store_id
        self.store_name = store_name
        self.store_city = store_city
        
        # Different category values to test
        self.test_categories = [
            {"type": "boutique", "value": "4653"},  # From HAR file
            {"type": "boutique", "value": "4654"},  # Try next
            {"type": "boutique", "value": "4655"},  # Try next
            {"type": "boutique", "value": "4652"},  # Try previous
            {"type": "boutique", "value": "4651"},  # Try previous
        ]

    def establish_store_context(self):
        """Establish store context for the working store"""
        try:
            log.info(f"🏪 Establishing context for {self.store_id} ({self.store_name})")
            
            # Visit store page
            store_url = f"{self.base_url}/magasins/{self.store_id}/{self.store_city}/infos-pratiques"
            store_response = self.session.get(store_url, headers=self.headers, timeout=30)
            log.info(f"📍 Store page: {store_response.status_code}")
            
            if store_response.status_code != 200:
                return False
            
            time.sleep(1)
            
            # GET /accueil
            get_accueil_headers = self.headers.copy()
            get_accueil_headers['Referer'] = store_url
            get_accueil_response = self.session.get(f"{self.base_url}/accueil", headers=get_accueil_headers, timeout=30)
            log.info(f"🏠 GET Accueil: {get_accueil_response.status_code}")
            
            time.sleep(1)
            
            # POST to /accueil
            post_accueil_headers = self.headers.copy()
            post_accueil_headers.update({
                'Accept': 'text/x-component',
                'Content-Type': 'text/plain;charset=UTF-8',
                'next-action': '0021d05dbc1bd15b63b38f154a7cf55a9cc8fea62c',
                'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2Faccueil%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            })
            
            post_accueil_response = self.session.post(f"{self.base_url}/accueil", headers=post_accueil_headers, data='[]', timeout=30)
            log.info(f"🚀 POST Accueil: {post_accueil_response.status_code}")
            
            return True
                
        except Exception as e:
            log.error(f"❌ Error establishing store context: {e}")
            return False

    def test_suggestion_api_categories(self):
        """Test suggestion API with different categories"""
        all_eans = set()
        
        for category in self.test_categories:
            try:
                log.info(f"🔍 Testing suggestion API with category {category['value']}")
                
                api_headers = self.headers.copy()
                api_headers.update({
                    'Accept': '*/*',
                    'Content-Type': 'application/json',
                    'Origin': self.base_url,
                    'Referer': f'{self.base_url}/accueil',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-origin',
                    'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                    'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                    'x-red-device': 'red_fo_desktop',
                    'x-red-version': '3',
                    'x-service-name': 'produits',
                    'x-optional-oauth': 'true',
                    'x-is-server': 'false'
                })
                
                suggestion_data = {
                    "productNumber": 20,  # Try to get more products
                    "criterias": [category]
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/service/produits/v1/pdvs/{self.store_id}/products/suggestion",
                    json=suggestion_data,
                    headers=api_headers,
                    timeout=30
                )
                
                log.info(f"📊 Category {category['value']}: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data and len(data) > 0 and 'products' in data[0]:
                        products = data[0]['products']
                        eans = [p.get('produitEan13') for p in products if p.get('produitEan13')]
                        log.info(f"✅ Got {len(eans)} EAN codes from category {category['value']}")
                        all_eans.update(eans)
                    else:
                        log.warning(f"⚠️ No products in response for category {category['value']}")
                else:
                    log.warning(f"⚠️ API failed for category {category['value']}: {response.status_code}")
                
                time.sleep(0.5)  # Small delay between requests
                
            except Exception as e:
                log.error(f"❌ Error testing category {category['value']}: {e}")
        
        log.info(f"🎯 Total unique EANs collected: {len(all_eans)}")
        return list(all_eans)

    def load_scraped_eans(self, filename="intermarche_eans.json", max_eans=100):
        """Load EANs from our scraped data"""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
                eans = data.get('eans', [])
                # Limit to max_eans for testing
                if len(eans) > max_eans:
                    eans = eans[:max_eans]
                log.info(f"📥 Loaded {len(eans)} EANs from {filename}")
                return eans
        except Exception as e:
            log.error(f"❌ Failed to load EANs from {filename}: {e}")
            return []

    def test_byeans_api_batches(self, all_eans):
        """Test byEans API with batches of EANs"""
        all_products = []
        batch_size = 20  # API seems to handle up to 20 EANs per request
        
        for i in range(0, len(all_eans), batch_size):
            batch_eans = all_eans[i:i+batch_size]
            batch_num = (i // batch_size) + 1
            
            try:
                log.info(f"🛒 Testing byEans API batch {batch_num} with {len(batch_eans)} EANs")
                
                api_headers = self.headers.copy()
                api_headers.update({
                    'Accept': '*/*',
                    'Content-Type': 'application/json',
                    'Origin': self.base_url,
                    'Referer': f'{self.base_url}/accueil',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-origin',
                    'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                    'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                    'x-red-device': 'red_fo_desktop',
                    'x-red-version': '3',
                    'x-service-name': 'produits',
                    'x-optional-oauth': 'true',
                    'x-is-server': 'false'
                })
                
                byeans_data = {"eans": batch_eans}
                
                response = self.session.post(
                    f"{self.base_url}/api/service/produits/v3/stores/{self.store_id}/products/byEans",
                    json=byeans_data,
                    headers=api_headers,
                    timeout=30
                )
                
                log.info(f"🛍️ Batch {batch_num}: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if 'products' in data:
                        products = data['products']
                        log.info(f"✅ Batch {batch_num}: Got {len(products)} products")
                        all_products.extend(products)
                        
                        # Log sample products from this batch
                        for j, product in enumerate(products[:2]):  # Show first 2 from each batch
                            name = product.get('libelle', 'Unknown')
                            price = product.get('prix', 0)
                            stock = product.get('stock', 0)
                            log.info(f"   📦 {name} - €{price} (Stock: {stock})")
                    else:
                        log.warning(f"⚠️ No products in batch {batch_num} response")
                else:
                    log.warning(f"⚠️ Batch {batch_num} failed: {response.status_code}")
                
                time.sleep(1)  # Delay between batches
                
            except Exception as e:
                log.error(f"❌ Error testing batch {batch_num}: {e}")
        
        return all_products

    def run_comprehensive_test(self):
        """Run comprehensive EAN-based test"""
        log.info("🚀 Starting comprehensive EAN-based API test")
        log.info("=" * 60)
        
        # Establish store context
        if not self.establish_store_context():
            log.error("❌ Failed to establish store context")
            return
        
        # Load scraped EANs instead of collecting from categories
        log.info("\n📋 Phase 1: Loading scraped EANs")
        log.info("-" * 40)
        all_eans = self.load_scraped_eans()

        if not all_eans:
            log.error("❌ No EANs loaded")
            return
        
        # Test byEans API with all collected EANs
        log.info(f"\n🛒 Phase 2: Getting product data for {len(all_eans)} EANs")
        log.info("-" * 40)
        all_products = self.test_byeans_api_batches(all_eans)
        
        # Final summary
        log.info("\n" + "=" * 60)
        log.info("📊 COMPREHENSIVE TEST RESULTS")
        log.info("=" * 60)
        log.info(f"🏪 Store: {self.store_id} ({self.store_name})")
        log.info(f"🔍 EANs collected: {len(all_eans)}")
        log.info(f"🛍️ Products retrieved: {len(all_products)}")
        
        if all_products:
            # Analyze product categories
            categories = {}
            for product in all_products:
                rayon = product.get('idRayon', 'Unknown')
                if rayon not in categories:
                    categories[rayon] = 0
                categories[rayon] += 1
            
            log.info(f"📈 Product categories found: {len(categories)}")
            for rayon, count in sorted(categories.items(), key=lambda x: x[1], reverse=True)[:5]:
                log.info(f"   - Rayon {rayon}: {count} products")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_ean_results_{timestamp}.json"
        
        results = {
            "store_id": self.store_id,
            "store_name": self.store_name,
            "eans_collected": len(all_eans),
            "products_retrieved": len(all_products),
            "eans": all_eans,
            "products": all_products
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        log.info(f"💾 Detailed results saved to {filename}")
        
        return results

def test_specific_ean_across_stores(target_ean):
    """Test a specific EAN across all working stores quickly"""

    log.info("🎯 SPECIFIC EAN MULTI-STORE TEST")
    log.info("================================================================================")
    log.info(f"Testing EAN: {target_ean} across all working stores")
    log.info("================================================================================")

    # Working stores from previous test (90% success rate)
    working_stores = [
        ("12118", "Super Marseille", "marseille-13004"),
        ("11691", "Intermarché Arbent", "arbent-01100"),
        ("05667", "Intermarché Béon", "beon-01350"),
        ("10525", "Intermarché Bourg-en-Bresse", "bourg-en-bresse-01000"),
        ("06877", "Intermarché Briord", "briord-01470"),
        ("10242", "Intermarché Châtillon", "chatillon-sur-chalaronne-01400"),
        ("10113", "Intermarché Chazey-Bons", "chazey-bons-01300"),
        ("01590", "Intermarché Feillens", "feillens-01570"),
        ("10946", "Intermarché Gex", "gex-01170"),
        ("10123", "Intermarché Jayat", "jayat-01340"),
        ("10453", "Intermarché La Boisse", "la-boisse-01120"),
        ("06066", "Intermarché Lavancia", "lavancia-epercy-01590"),
        ("10184", "Intermarché Meximieux", "meximieux-01800"),
        ("50332", "Intermarché Neuville", "neuville-sur-ain-01160"),
        ("06909", "Intermarché Oyonnax", "oyonnax-01100"),
        ("05421", "Intermarché Péron", "peron-01630"),
        ("07480", "Intermarché Polliat", "polliat-01310"),
        ("10465", "Intermarché Port", "port-01460"),
    ]

    successful_stores = []
    failed_stores = []
    all_results = []

    for i, (store_id, store_name, store_city) in enumerate(working_stores, 1):
        log.info(f"\n{'='*60}")
        log.info(f"TESTING STORE {i}/18: {store_name} ({store_id})")
        log.info(f"{'='*60}")

        try:
            # Create tester instance for this store
            tester = ComprehensiveEANTester(store_id, store_name, store_city)

            # Establish store context
            log.info(f"🏪 Establishing context for {store_id}")
            if not tester.establish_store_context():
                log.error(f"❌ Failed to establish store context")
                failed_stores.append({
                    'store_id': store_id,
                    'store_name': store_name,
                    'store_city': store_city,
                    'error': 'Failed to establish context'
                })
                continue

            # Test the specific EAN directly
            log.info(f"🎯 Testing EAN {target_ean}")

            # Use byEans API directly with the target EAN (using correct URL and headers)
            api_headers = tester.headers.copy()
            api_headers.update({
                'Accept': '*/*',
                'Content-Type': 'application/json',
                'Origin': tester.base_url,
                'Referer': f'{tester.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-red-device': 'red_fo_desktop',
                'x-red-version': '3',
                'x-service-name': 'produits',
                'x-optional-oauth': 'true',
                'x-is-server': 'false'
            })

            byeans_data = {"eans": [target_ean]}

            response = tester.session.post(
                f"{tester.base_url}/api/service/produits/v3/stores/{store_id}/products/byEans",
                json=byeans_data,
                headers=api_headers,
                timeout=30
            )
            log.info(f"🛍️ byEans API: {response.status_code}")

            if response.status_code == 200:
                products_data = response.json()
                log.info(f"🔍 API Response: {products_data}")

                # Handle different response structures
                if isinstance(products_data, dict):
                    products = products_data.get('products', [])
                elif isinstance(products_data, list):
                    products = products_data
                else:
                    log.warning(f"⚠️ Unexpected response type: {type(products_data)}")
                    products = []

                if products:
                    product = products[0]
                    # Extract product details using correct field names
                    price = product.get('prix', 'N/A')
                    stock = product.get('stock', 'N/A')
                    name = product.get('libelle', product.get('name', 'Unknown'))

                    log.info(f"✅ FOUND: {name} - €{price} (Stock: {stock})")

                    successful_stores.append({
                        'store_id': store_id,
                        'store_name': store_name,
                        'store_city': store_city,
                        'product_name': name,
                        'price': price,
                        'stock': stock,
                        'ean': target_ean
                    })

                    all_results.append({
                        'store_id': store_id,
                        'store_name': store_name,
                        'product': product
                    })
                else:
                    log.warning(f"⚠️ EAN not found in this store")
                    failed_stores.append({
                        'store_id': store_id,
                        'store_name': store_name,
                        'store_city': store_city,
                        'error': 'EAN not found'
                    })
            else:
                log.error(f"❌ API failed: {response.status_code}")
                failed_stores.append({
                    'store_id': store_id,
                    'store_name': store_name,
                    'store_city': store_city,
                    'error': f'API error {response.status_code}'
                })

        except Exception as e:
            log.error(f"❌ Exception: {str(e)}")
            failed_stores.append({
                'store_id': store_id,
                'store_name': store_name,
                'store_city': store_city,
                'error': str(e)
            })

        # Shorter delay for faster testing
        if i < len(working_stores):
            delay = 5  # 5 seconds between stores for speed
            log.info(f"⏳ Waiting {delay} seconds...")
            time.sleep(delay)

    # Summary
    log.info(f"\n{'='*80}")
    log.info("EAN MULTI-STORE TEST SUMMARY")
    log.info(f"{'='*80}")
    log.info(f"EAN tested: {target_ean}")
    log.info(f"Total stores tested: {len(working_stores)}")
    log.info(f"Stores with product: {len(successful_stores)}")
    log.info(f"Stores without product: {len(failed_stores)}")
    log.info(f"Success rate: {len(successful_stores)/len(working_stores)*100:.1f}%")

    if successful_stores:
        log.info(f"\n✅ STORES WITH PRODUCT:")
        for store in successful_stores:
            log.info(f"   - {store['store_id']} ({store['store_name']}): €{store['price']} (Stock: {store['stock']})")

    if failed_stores:
        log.info(f"\n❌ STORES WITHOUT PRODUCT:")
        for store in failed_stores:
            log.info(f"   - {store['store_id']} ({store['store_name']}): {store['error']}")

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"ean_multistore_results_{target_ean}_{timestamp}.json"

    results = {
        "ean": target_ean,
        "total_stores": len(working_stores),
        "successful_stores": successful_stores,
        "failed_stores": failed_stores,
        "all_results": all_results
    }

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    log.info(f"\n💾 Results saved to: {filename}")

    return results

def test_multiple_stores():
    """Test multiple stores using the working comprehensive approach"""

    log.info("🌟 MULTI-STORE COMPREHENSIVE EAN TEST")
    log.info("=" * 80)
    log.info("Using the PROVEN working comprehensive test approach on multiple stores")
    log.info("=" * 80)

    # Test 20 different stores from the database
    test_stores = [
        ("12118", "Super Marseille", "marseille-13004"),  # Known working
        ("10498", "Intermarché Ambérieu", "amberieu-en-bugey-01500"),
        ("11691", "Intermarché Arbent", "arbent-01100"),
        ("05667", "Intermarché Béon", "beon-01350"),
        ("10823", "Intermarché Beynost", "beynost-01700"),
        ("10525", "Intermarché Bourg-en-Bresse", "bourg-en-bresse-01000"),
        ("06877", "Intermarché Briord", "briord-01470"),
        ("10242", "Intermarché Châtillon", "chatillon-sur-chalaronne-01400"),
        ("10113", "Intermarché Chazey-Bons", "chazey-bons-01300"),
        ("01590", "Intermarché Feillens", "feillens-01570"),
        ("10946", "Intermarché Gex", "gex-01170"),
        ("10123", "Intermarché Jayat", "jayat-01340"),
        ("10453", "Intermarché La Boisse", "la-boisse-01120"),
        ("06066", "Intermarché Lavancia", "lavancia-epercy-01590"),
        ("10184", "Intermarché Meximieux", "meximieux-01800"),
        ("50332", "Intermarché Neuville", "neuville-sur-ain-01160"),
        ("06909", "Intermarché Oyonnax", "oyonnax-01100"),
        ("05421", "Intermarché Péron", "peron-01630"),
        ("07480", "Intermarché Polliat", "polliat-01310"),
        ("10465", "Intermarché Port", "port-01460"),
    ]

    all_results = []
    successful_stores = []
    failed_stores = []

    for i, (store_id, store_name, store_city) in enumerate(test_stores, 1):
        log.info(f"\n{'='*80}")
        log.info(f"TESTING STORE {i}/20: {store_name} ({store_id})")
        log.info(f"{'='*80}")

        try:
            # Create fresh tester instance for each store
            tester = ComprehensiveEANTester(store_id, store_name, store_city)

            # Run the proven comprehensive test
            result = tester.run_comprehensive_test()

            if result and result.get('products_retrieved', 0) > 0:
                log.info(f"✅ SUCCESS: {store_name} - {result['products_retrieved']} products")
                successful_stores.append({
                    'store_id': store_id,
                    'store_name': store_name,
                    'store_city': store_city,
                    'products': result['products_retrieved'],
                    'eans': result['eans_collected']
                })
            else:
                log.error(f"❌ FAILED: {store_name} - No products retrieved")
                failed_stores.append({
                    'store_id': store_id,
                    'store_name': store_name,
                    'store_city': store_city,
                    'error': 'No products retrieved'
                })

            all_results.append(result)

        except Exception as e:
            log.error(f"💥 ERROR testing {store_name}: {str(e)}")
            failed_stores.append({
                'store_id': store_id,
                'store_name': store_name,
                'store_city': store_city,
                'error': str(e)
            })

        # Critical delay between stores to avoid rate limiting
        if i < len(test_stores):
            delay = 20  # 20 seconds between stores
            log.info(f"⏳ Waiting {delay} seconds before next store to avoid rate limiting...")
            time.sleep(delay)

    # Final summary
    log.info(f"\n{'='*80}")
    log.info("MULTI-STORE TEST SUMMARY")
    log.info(f"{'='*80}")
    log.info(f"Total stores tested: {len(test_stores)}")
    log.info(f"Successful stores: {len(successful_stores)}")
    log.info(f"Failed stores: {len(failed_stores)}")
    log.info(f"Success rate: {len(successful_stores)/len(test_stores)*100:.1f}%")

    if successful_stores:
        log.info(f"\n✅ WORKING STORES:")
        for store in successful_stores:
            log.info(f"   - {store['store_id']} ({store['store_name']}): {store['products']} products")

    if failed_stores:
        log.info(f"\n❌ FAILED STORES:")
        for store in failed_stores:
            log.info(f"   - {store['store_id']} ({store['store_name']}): {store.get('error', 'Unknown error')}")

    # Save comprehensive results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"multi_store_comprehensive_results_{timestamp}.json"

    summary_data = {
        'timestamp': timestamp,
        'total_stores': len(test_stores),
        'successful_stores': successful_stores,
        'failed_stores': failed_stores,
        'success_rate': len(successful_stores)/len(test_stores)*100,
        'detailed_results': all_results
    }

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, indent=2, ensure_ascii=False)

    log.info(f"\n💾 Comprehensive results saved to: {results_file}")

    return summary_data

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "multi":
            # Run multi-store test
            test_multiple_stores()
        elif sys.argv[1] == "ean" and len(sys.argv) > 2:
            # Test specific EAN across all stores
            target_ean = sys.argv[2]
            test_specific_ean_across_stores(target_ean)
        else:
            print("Usage:")
            print("  python comprehensive_ean_test.py          # Single store test")
            print("  python comprehensive_ean_test.py multi    # Multi-store test")
            print("  python comprehensive_ean_test.py ean <EAN_CODE>  # Test specific EAN across all stores")
    else:
        # Run single store test (original behavior)
        tester = ComprehensiveEANTester()
        results = tester.run_comprehensive_test()
