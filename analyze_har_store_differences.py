#!/usr/bin/env python3
"""
Analyze HAR files to understand why store 12118 works but others don't
"""

import json
import re
from urllib.parse import urlparse, parse_qs

def analyze_har_file(har_file_path):
    """Analyze HAR file to extract store-specific patterns"""
    
    print(f"\n🔍 ANALYZING HAR FILE: {har_file_path}")
    print("=" * 60)
    
    try:
        with open(har_file_path, 'r', encoding='utf-8') as f:
            har_data = json.load(f)
    except Exception as e:
        print(f"❌ Error reading HAR file: {e}")
        return
    
    entries = har_data.get('log', {}).get('entries', [])
    print(f"📊 Total entries: {len(entries)}")
    
    # Analyze store-related requests
    store_requests = []
    api_requests = []
    accueil_requests = []
    
    for entry in entries:
        request = entry.get('request', {})
        response = entry.get('response', {})
        url = request.get('url', '')
        method = request.get('method', '')
        status = response.get('status', 0)
        
        # Extract store ID from URL
        store_match = re.search(r'/magasins/(\d+)/', url)
        store_id = store_match.group(1) if store_match else None
        
        # Categorize requests
        if '/magasins/' in url:
            store_requests.append({
                'url': url,
                'method': method,
                'status': status,
                'store_id': store_id
            })
            
            if '/accueil' in url:
                accueil_requests.append({
                    'url': url,
                    'method': method,
                    'status': status,
                    'store_id': store_id,
                    'headers': request.get('headers', []),
                    'postData': request.get('postData', {})
                })
        
        if '/api/' in url:
            api_requests.append({
                'url': url,
                'method': method,
                'status': status,
                'store_id': store_id
            })
    
    print(f"\n📍 STORE REQUESTS: {len(store_requests)}")
    unique_stores = set()
    for req in store_requests:
        if req['store_id']:
            unique_stores.add(req['store_id'])
            print(f"   {req['method']} {req['url']} -> {req['status']}")
    
    print(f"\n🏪 UNIQUE STORES FOUND: {len(unique_stores)}")
    for store_id in sorted(unique_stores):
        print(f"   Store ID: {store_id}")
    
    print(f"\n🏠 ACCUEIL REQUESTS: {len(accueil_requests)}")
    for req in accueil_requests:
        print(f"   {req['method']} {req['url']} -> {req['status']}")
        
        # Analyze POST data for accueil requests
        if req['method'] == 'POST' and req['postData']:
            post_data = req['postData'].get('text', '')
            print(f"      POST data: {post_data}")
        
        # Check for special headers
        headers = {h['name']: h['value'] for h in req['headers']}
        special_headers = ['next-action', 'next-router-state-tree', 'x-itm-session-id']
        for header in special_headers:
            if header in headers:
                print(f"      {header}: {headers[header][:50]}...")
    
    print(f"\n🔌 API REQUESTS: {len(api_requests)}")
    for req in api_requests:
        print(f"   {req['method']} {req['url']} -> {req['status']}")
    
    # Look for patterns that might indicate why 12118 works
    print(f"\n🔍 ANALYSIS FOR STORE 12118:")
    store_12118_requests = [req for req in store_requests if req['store_id'] == '12118']
    print(f"   Store 12118 requests: {len(store_12118_requests)}")
    
    for req in store_12118_requests:
        print(f"   ✅ {req['method']} {req['url']} -> {req['status']}")
    
    # Check if there are any other stores in the HAR
    other_stores = [store_id for store_id in unique_stores if store_id != '12118']
    if other_stores:
        print(f"\n🔍 OTHER STORES IN HAR:")
        for store_id in other_stores:
            other_store_requests = [req for req in store_requests if req['store_id'] == store_id]
            print(f"   Store {store_id} requests: {len(other_store_requests)}")
            for req in other_store_requests:
                print(f"   📍 {req['method']} {req['url']} -> {req['status']}")
    
    # Look for specific URL patterns
    print(f"\n🔍 URL PATTERN ANALYSIS:")
    url_patterns = {}
    for req in store_requests:
        parsed = urlparse(req['url'])
        path_parts = parsed.path.split('/')
        if len(path_parts) >= 4 and path_parts[1] == 'magasins':
            pattern = f"/{path_parts[1]}/{path_parts[2]}/{path_parts[3]}/{path_parts[4] if len(path_parts) > 4 else ''}"
            if pattern not in url_patterns:
                url_patterns[pattern] = []
            url_patterns[pattern].append(req)
    
    for pattern, requests in url_patterns.items():
        print(f"   Pattern: {pattern}")
        for req in requests:
            print(f"      Store {req['store_id']}: {req['status']}")
    
    return {
        'total_entries': len(entries),
        'store_requests': len(store_requests),
        'api_requests': len(api_requests),
        'unique_stores': list(unique_stores),
        'accueil_requests': accueil_requests
    }

def compare_working_vs_failing():
    """Compare patterns between working and failing scenarios"""
    
    print(f"\n{'='*80}")
    print("COMPARING WORKING VS FAILING PATTERNS")
    print(f"{'='*80}")
    
    # Analyze the full process HAR (which should contain working 12118)
    print("\n🟢 ANALYZING WORKING SCENARIO (FullProcess.har):")
    working_analysis = analyze_har_file('FullProcess.har')
    
    # If we have context.har, analyze that too
    try:
        print("\n🔍 ANALYZING CONTEXT HAR (context.har):")
        context_analysis = analyze_har_file('context.har')
    except:
        print("⚠️ context.har not found or not readable")
        context_analysis = None
    
    # Summary
    print(f"\n{'='*80}")
    print("SUMMARY OF FINDINGS")
    print(f"{'='*80}")
    
    if working_analysis:
        print(f"✅ Working scenario (FullProcess.har):")
        print(f"   - Unique stores: {working_analysis['unique_stores']}")
        print(f"   - Store requests: {working_analysis['store_requests']}")
        print(f"   - API requests: {working_analysis['api_requests']}")
    
    if context_analysis:
        print(f"🔍 Context scenario (context.har):")
        print(f"   - Unique stores: {context_analysis['unique_stores']}")
        print(f"   - Store requests: {context_analysis['store_requests']}")
        print(f"   - API requests: {context_analysis['api_requests']}")

def extract_working_store_sequence():
    """Extract the exact sequence that works for store 12118"""
    
    print(f"\n{'='*80}")
    print("EXTRACTING WORKING SEQUENCE FOR STORE 12118")
    print(f"{'='*80}")
    
    try:
        with open('FullProcess.har', 'r', encoding='utf-8') as f:
            har_data = json.load(f)
    except Exception as e:
        print(f"❌ Error reading FullProcess.har: {e}")
        return
    
    entries = har_data.get('log', {}).get('entries', [])
    
    # Filter for store 12118 requests
    store_12118_sequence = []
    
    for entry in entries:
        request = entry.get('request', {})
        response = entry.get('response', {})
        url = request.get('url', '')
        
        # Check if this is a store 12118 request
        if '/magasins/12118/' in url or ('/api/' in url and '12118' in url):
            store_12118_sequence.append({
                'timestamp': entry.get('startedDateTime', ''),
                'url': url,
                'method': request.get('method', ''),
                'status': response.get('status', 0),
                'headers': request.get('headers', []),
                'postData': request.get('postData', {}),
                'response_headers': response.get('headers', [])
            })
    
    print(f"📋 STORE 12118 REQUEST SEQUENCE ({len(store_12118_sequence)} requests):")
    print("-" * 60)
    
    for i, req in enumerate(store_12118_sequence, 1):
        print(f"{i:2d}. {req['method']} {req['url']}")
        print(f"    Status: {req['status']}")
        print(f"    Time: {req['timestamp']}")
        
        # Show important headers
        headers = {h['name'].lower(): h['value'] for h in req['headers']}
        important_headers = ['referer', 'origin', 'x-itm-session-id', 'next-action']
        for header in important_headers:
            if header in headers:
                value = headers[header]
                if len(value) > 50:
                    value = value[:50] + "..."
                print(f"    {header}: {value}")
        
        # Show POST data
        if req['postData'] and req['postData'].get('text'):
            post_data = req['postData']['text']
            if len(post_data) > 100:
                post_data = post_data[:100] + "..."
            print(f"    POST: {post_data}")
        
        print()
    
    # Save the sequence for reference
    with open('store_12118_working_sequence.json', 'w', encoding='utf-8') as f:
        json.dump(store_12118_sequence, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Working sequence saved to: store_12118_working_sequence.json")

def main():
    """Main analysis function"""
    
    print("🔍 HAR FILE ANALYSIS - Understanding Store 12118 Success")
    print("=" * 80)
    
    # Compare working vs failing patterns
    compare_working_vs_failing()
    
    # Extract the exact working sequence
    extract_working_store_sequence()
    
    print(f"\n{'='*80}")
    print("ANALYSIS COMPLETE")
    print(f"{'='*80}")
    print("Key files generated:")
    print("- store_12118_working_sequence.json: Exact sequence that works")
    print("\nNext steps:")
    print("1. Compare this sequence with our failed attempts")
    print("2. Look for differences in headers, timing, or request order")
    print("3. Test if other stores follow the same pattern")

if __name__ == "__main__":
    main()
