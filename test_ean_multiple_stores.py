#!/usr/bin/env python3
"""
Test EAN-based API approach with multiple stores using store mappings from City page.html
"""

import requests
import json
import time
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

class IntermarheEANTester:
    def __init__(self):
        self.base_url = "https://www.intermarche.com"
        self.session = requests.Session()
        
        # Headers from successful HAR requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'fr-FR,fr;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        
        # EAN codes from the HAR file suggestion API response
        self.test_eans = [
            "3021762412464",  # Pom'Potes compotes
            "5038862160506",  # Innocent orange juice
            "8715700421957",  # Heinz mayonnaise
            "3017620429484",  # Nutella
            "8720182473813",  # Amora mayo
            "8720182305930",  # Amora sauce algérienne
            "8715700421841",  # Heinz American Burger
            "8720182587213",  # Amora sauce chinoise
            "8715700422978",  # Heinz sauce pommes frites
            "3033491279706",  # Danette chocolat
            "3033491432743",  # Danette vanille chocolat
            "3033491432774",  # Danette 3 chocolats
            "3033491213144",  # Actimel fraise
            "3250390012276",  # Pâturages riz au lait nature
            "3250390822929",  # Pâturages riz au lait caramel
            "3372900901075"   # Marie Morin mousse chocolat
        ]
        
        # Test stores (real store IDs - trying patterns around working store 12118)
        self.test_stores = [
            {"id": "12118", "name": "Super Marseille", "city": "marseille-13004"},
            {"id": "12117", "name": "Store 12117", "city": "generic"},  # Nearby ID
            {"id": "12119", "name": "Store 12119", "city": "generic"},  # Nearby ID
            {"id": "12001", "name": "Store 12001", "city": "generic"},  # Same prefix
            {"id": "11118", "name": "Store 11118", "city": "generic"},  # Similar pattern
        ]

    def establish_store_context(self, store_id, store_name, city="generic"):
        """Establish store context by visiting store page and posting to accueil"""
        try:
            log.info(f"🏪 Establishing context for {store_id} ({store_name})")
            
            # Step 1: Visit store page
            if city == "generic":
                store_url = f"{self.base_url}/magasins/{store_id}"
            else:
                store_url = f"{self.base_url}/magasins/{store_id}/{city}/infos-pratiques"
            
            store_response = self.session.get(store_url, headers=self.headers, timeout=30)
            log.info(f"📍 Store page: {store_response.status_code}")
            
            if store_response.status_code != 200:
                log.warning(f"⚠️ Store page failed for {store_id}")
                return False
            
            # Check store context from response
            store_hash = store_response.headers.get('x-custom-hash', '')
            log.info(f"🏪 Store hash: {store_hash}")
            
            time.sleep(1)
            
            # Step 2: GET /accueil
            get_accueil_headers = self.headers.copy()
            get_accueil_headers['Referer'] = store_url
            
            get_accueil_response = self.session.get(f"{self.base_url}/accueil", headers=get_accueil_headers, timeout=30)
            log.info(f"🏠 GET Accueil: {get_accueil_response.status_code}")
            
            time.sleep(1)
            
            # Step 3: POST to /accueil (Next.js Server Action)
            post_accueil_headers = self.headers.copy()
            post_accueil_headers.update({
                'Accept': 'text/x-component',
                'Content-Type': 'text/plain;charset=UTF-8',
                'next-action': '0021d05dbc1bd15b63b38f154a7cf55a9cc8fea62c',
                'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2Faccueil%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            })
            
            post_accueil_response = self.session.post(f"{self.base_url}/accueil", headers=post_accueil_headers, data='[]', timeout=30)
            log.info(f"🚀 POST Accueil: {post_accueil_response.status_code}")
            
            # Check final store context
            final_hash = post_accueil_response.headers.get('x-custom-hash', '')
            log.info(f"🎯 Final hash: {final_hash}")
            
            if store_id in final_hash:
                log.info(f"✅ Store context established successfully!")
                return True
            else:
                log.warning(f"⚠️ Store context not fully established")
                return True  # Continue anyway to test API
                
        except Exception as e:
            log.error(f"❌ Error establishing store context: {e}")
            return False

    def test_suggestion_api(self, store_id):
        """Test the suggestion API to get EAN codes"""
        try:
            log.info(f"🔍 Testing suggestion API for store {store_id}")
            
            api_headers = self.headers.copy()
            api_headers.update({
                'Accept': '*/*',
                'Content-Type': 'application/json',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-red-device': 'red_fo_desktop',
                'x-red-version': '3',  # MISSING HEADER!
                'x-service-name': 'produits',
                'x-optional-oauth': 'true',
                'x-is-server': 'false'
            })
            
            suggestion_data = {
                "productNumber": 16,
                "criterias": [{"type": "boutique", "value": "4653"}]
            }
            
            response = self.session.post(
                f"{self.base_url}/api/service/produits/v1/pdvs/{store_id}/products/suggestion",
                json=suggestion_data,
                headers=api_headers,
                timeout=30
            )
            
            log.info(f"📊 Suggestion API: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0 and 'products' in data[0]:
                    products = data[0]['products']
                    eans = [p.get('produitEan13') for p in products if p.get('produitEan13')]
                    log.info(f"✅ Got {len(eans)} EAN codes from suggestion API")
                    return eans[:10]  # Return first 10 for testing
                else:
                    log.warning(f"⚠️ No products in suggestion response")
                    return []
            else:
                log.warning(f"⚠️ Suggestion API failed: {response.status_code}")
                return []
                
        except Exception as e:
            log.error(f"❌ Error testing suggestion API: {e}")
            return []

    def test_byeans_api(self, store_id, eans):
        """Test the byEans API to get product data"""
        try:
            log.info(f"🛒 Testing byEans API for store {store_id} with {len(eans)} EANs")
            
            api_headers = self.headers.copy()
            api_headers.update({
                'Accept': '*/*',
                'Content-Type': 'application/json',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/accueil',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
                'x-red-device': 'red_fo_desktop',
                'x-red-version': '3',  # MISSING HEADER!
                'x-service-name': 'produits',
                'x-optional-oauth': 'true',
                'x-is-server': 'false'
            })
            
            byeans_data = {"eans": eans}
            
            response = self.session.post(
                f"{self.base_url}/api/service/produits/v3/stores/{store_id}/products/byEans",
                json=byeans_data,
                headers=api_headers,
                timeout=30
            )
            
            log.info(f"🛍️ ByEans API: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if 'products' in data:
                    products = data['products']
                    log.info(f"✅ Got {len(products)} products from byEans API")
                    
                    # Log sample product data
                    for i, product in enumerate(products[:3]):  # Show first 3
                        name = product.get('libelle', 'Unknown')
                        price = product.get('prix', 0)
                        stock = product.get('stock', 0)
                        ean = product.get('produitEan13', 'Unknown')
                        log.info(f"   📦 {i+1}. {name} - €{price} (Stock: {stock}) [EAN: {ean}]")
                    
                    return products
                else:
                    log.warning(f"⚠️ No products in byEans response")
                    return []
            else:
                log.warning(f"⚠️ ByEans API failed: {response.status_code}")
                if response.status_code == 400:
                    log.info(f"Response: {response.text[:200]}")
                return []
                
        except Exception as e:
            log.error(f"❌ Error testing byEans API: {e}")
            return []

    def run_test(self):
        """Run the complete test across multiple stores"""
        log.info("🚀 Starting EAN-based API test across multiple stores")
        log.info("=" * 60)
        
        results = {}
        
        for store in self.test_stores:
            store_id = store["id"]
            store_name = store["name"]
            city = store["city"]
            
            log.info(f"\n🏪 Testing store {store_id} ({store_name})")
            log.info("-" * 40)
            
            # Establish store context
            context_success = self.establish_store_context(store_id, store_name, city)
            
            if not context_success:
                log.error(f"❌ Failed to establish context for {store_id}")
                results[store_id] = {"error": "Context establishment failed"}
                continue
            
            # Test suggestion API
            suggestion_eans = self.test_suggestion_api(store_id)
            
            # Use suggestion EANs if available, otherwise use test EANs
            test_eans = suggestion_eans if suggestion_eans else self.test_eans[:10]
            
            # Test byEans API
            products = self.test_byeans_api(store_id, test_eans)
            
            results[store_id] = {
                "store_name": store_name,
                "context_established": context_success,
                "suggestion_eans": len(suggestion_eans),
                "products_found": len(products),
                "products": products[:5] if products else []  # Store first 5 for analysis
            }
            
            log.info(f"📊 Store {store_id} summary: {len(products)} products found")
            
            # Add delay between stores
            time.sleep(2)
        
        # Print final summary
        log.info("\n" + "=" * 60)
        log.info("📊 FINAL TEST RESULTS")
        log.info("=" * 60)
        
        for store_id, result in results.items():
            if "error" in result:
                log.info(f"❌ {store_id}: {result['error']}")
            else:
                log.info(f"✅ {store_id} ({result['store_name']}): {result['products_found']} products")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ean_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        log.info(f"💾 Detailed results saved to {filename}")
        
        return results

if __name__ == "__main__":
    tester = IntermarheEANTester()
    results = tester.run_test()
