#!/usr/bin/env python3
"""
Browser-based EAN Collector for Intermarché
Uses Selenium to navigate like a real user and collect thousands of EANs
Includes feedback loop with HTML debugging and iterative improvements
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import json
import csv
import logging
import re
from datetime import datetime
import os
from bs4 import BeautifulSoup

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('browser_ean_collector.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
log = logging.getLogger(__name__)

class BrowserEANCollector:
    def __init__(self, headless=False):
        self.headless = headless
        self.driver = None
        self.wait = None
        self.collected_eans = set()
        self.debug_counter = 0
        
        # Search terms for product discovery
        self.search_terms = [
            "lait", "pain", "eau", "yaourt", "fromage", "viande", "poisson", 
            "fruits", "légumes", "riz", "pâtes", "huile", "beurre", "oeufs",
            "chocolat", "café", "thé", "sucre", "farine", "tomates", "pommes",
            "bananes", "carottes", "pommes de terre", "salade", "jambon",
            "poulet", "boeuf", "saumon", "crevettes", "pizza", "soupe"
        ]
        
        self.base_url = "https://www.intermarche.com"
        
    def setup_driver(self):
        """Setup Chrome driver with appropriate options"""
        log.info("Setting up Chrome driver...")
        
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            log.info("Chrome driver setup successful")
            return True
        except Exception as e:
            log.error(f"Failed to setup Chrome driver: {e}")
            return False
    
    def save_debug_html(self, filename_suffix=""):
        """Save current page HTML for debugging"""
        self.debug_counter += 1
        filename = f"debug_browser_{self.debug_counter}_{filename_suffix}.html"
        
        try:
            html_content = self.driver.page_source
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            log.info(f"Debug HTML saved: {filename}")
            return filename
        except Exception as e:
            log.error(f"Failed to save debug HTML: {e}")
            return None
    
    def navigate_to_store(self, store_id, store_city):
        """Navigate to a specific store and establish context"""
        log.info(f"Navigating to store {store_id} ({store_city})")

        try:
            # Go to store page
            store_url = f"{self.base_url}/magasins/{store_id}/{store_city}/infos-pratiques"
            log.info(f"Loading store page: {store_url}")
            self.driver.get(store_url)

            # Wait for page to fully load
            log.info("Waiting for page to load completely...")
            time.sleep(8)  # Increased wait time

            # Save debug HTML
            self.save_debug_html(f"store_page_{store_id}")

            # Look for "Faire mes courses" button with more specific selector
            log.info("Looking for 'Faire mes courses' button...")

            # More specific selector based on the actual HTML structure
            shopping_button_selectors = [
                "//button[@aria-label='Faire mes courses']",
                "//button[contains(text(), 'Faire mes courses')]",
                "//a[contains(text(), 'Faire mes courses')]",
                "//button[contains(@class, 'stime_button--red') and contains(text(), 'Faire mes courses')]",
                "//div[contains(text(), 'Faire mes courses')]/parent::button",
                "//button[.//div[contains(text(), 'Faire mes courses')]]"
            ]

            shopping_button = None
            for i, selector in enumerate(shopping_button_selectors):
                try:
                    log.info(f"Trying selector {i+1}: {selector}")
                    shopping_button = WebDriverWait(self.driver, 15).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    log.info(f"Found shopping button with selector {i+1}: {selector}")
                    break
                except TimeoutException:
                    log.info(f"Selector {i+1} failed, trying next...")
                    continue

            if shopping_button:
                log.info("Clicking 'Faire mes courses' button...")

                # Scroll to button to ensure it's visible
                self.driver.execute_script("arguments[0].scrollIntoView(true);", shopping_button)
                time.sleep(2)

                # Try clicking with JavaScript if regular click fails
                try:
                    shopping_button.click()
                except Exception as e:
                    log.warning(f"Regular click failed: {e}, trying JavaScript click")
                    self.driver.execute_script("arguments[0].click();", shopping_button)

                log.info("Button clicked, waiting for navigation...")
                time.sleep(8)  # Wait for navigation

                # Save debug HTML after clicking
                self.save_debug_html(f"after_courses_click_{store_id}")

                log.info("Successfully navigated to shopping section")
                return True
            else:
                log.warning("Could not find 'Faire mes courses' button")
                # Try to navigate directly to shopping page
                shopping_url = f"{self.base_url}/accueil"
                log.info(f"Trying direct navigation to: {shopping_url}")
                self.driver.get(shopping_url)
                time.sleep(5)

                self.save_debug_html(f"direct_shopping_{store_id}")
                return True

        except Exception as e:
            log.error(f"Error navigating to store: {e}")
            self.save_debug_html(f"error_navigation_{store_id}")
            return False
    
    def search_products(self, search_term, max_pages=3):
        """Search for products and collect EANs"""
        log.info(f"Searching for products: '{search_term}'")
        collected_eans = set()

        try:
            # Wait for page to be ready
            time.sleep(3)

            # Find search box with more comprehensive selectors
            search_selectors = [
                "//input[@id='search-input__input']",  # From the HTML structure we saw
                "//input[@type='search']",
                "//input[contains(@placeholder, 'recherche')]",
                "//input[contains(@placeholder, 'Recherche')]",
                "//input[contains(@placeholder, 'Rechercher')]",
                "//input[contains(@class, 'search')]",
                "//input[@name='q']",
                "//input[@id='search']",
                "//input[contains(@aria-label, 'recherche')]"
            ]

            search_box = None
            for i, selector in enumerate(search_selectors):
                try:
                    log.info(f"Trying search selector {i+1}: {selector}")
                    search_box = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    log.info(f"Found search box with selector {i+1}: {selector}")
                    break
                except TimeoutException:
                    log.info(f"Search selector {i+1} failed, trying next...")
                    continue

            if not search_box:
                log.error("Could not find search box")
                self.save_debug_html(f"no_search_box_{search_term}")

                # Try to find any input field as fallback
                try:
                    all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                    log.info(f"Found {len(all_inputs)} input elements on page")
                    for i, inp in enumerate(all_inputs[:5]):  # Check first 5 inputs
                        try:
                            inp_type = inp.get_attribute("type")
                            inp_placeholder = inp.get_attribute("placeholder")
                            inp_id = inp.get_attribute("id")
                            log.info(f"Input {i+1}: type='{inp_type}', placeholder='{inp_placeholder}', id='{inp_id}'")
                        except:
                            pass
                except Exception as e:
                    log.error(f"Error analyzing input fields: {e}")

                return collected_eans

            # Clear and enter search term
            log.info(f"Entering search term: '{search_term}'")
            search_box.clear()
            time.sleep(1)
            search_box.send_keys(search_term)
            time.sleep(1)
            search_box.send_keys(Keys.RETURN)

            log.info(f"Submitted search for: {search_term}")
            time.sleep(8)  # Wait longer for search results

            # Save search results page
            self.save_debug_html(f"search_results_{search_term}")

            # Process search results pages
            for page in range(1, max_pages + 1):
                log.info(f"Processing search results page {page}")

                # Extract EANs from current page
                page_eans = self.extract_eans_from_page(search_term, page)
                collected_eans.update(page_eans)

                log.info(f"Found {len(page_eans)} EANs on page {page}")

                # Try to go to next page
                if page < max_pages:
                    if not self.go_to_next_page():
                        log.info("No more pages available")
                        break
                    time.sleep(5)  # Longer wait between pages

        except Exception as e:
            log.error(f"Error during search: {e}")
            self.save_debug_html(f"search_error_{search_term}")

        log.info(f"Total EANs collected for '{search_term}': {len(collected_eans)}")
        return collected_eans
    
    def extract_eans_from_page(self, search_term, page):
        """Extract EANs from current page using multiple strategies"""
        eans = set()
        
        try:
            # Get page source and parse with BeautifulSoup
            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')
            
            log.info(f"Analyzing page for EANs (search: {search_term}, page: {page})")
            
            # Strategy 1: Look for EAN in data attributes
            for element in soup.find_all(attrs={'data-ean': True}):
                ean = element.get('data-ean')
                if self.is_valid_ean(ean):
                    eans.add(ean)
                    log.info(f"Found EAN in data-ean: {ean}")
            
            # Strategy 2: Look for EAN in product IDs
            for element in soup.find_all(attrs={'data-product-id': True}):
                product_id = element.get('data-product-id')
                if self.is_valid_ean(product_id):
                    eans.add(product_id)
                    log.info(f"Found EAN in product-id: {product_id}")
            
            # Strategy 3: Look for EAN patterns in text and scripts
            ean_pattern = re.compile(r'\b\d{13}\b')
            
            # Check script tags for JSON data
            for script in soup.find_all('script'):
                if script.string:
                    script_eans = ean_pattern.findall(script.string)
                    for ean in script_eans:
                        if self.is_valid_ean(ean):
                            eans.add(ean)
                            log.info(f"Found EAN in script: {ean}")
            
            # Strategy 4: Look in URLs and links
            for link in soup.find_all('a', href=True):
                href = link['href']
                url_eans = ean_pattern.findall(href)
                for ean in url_eans:
                    if self.is_valid_ean(ean):
                        eans.add(ean)
                        log.info(f"Found EAN in URL: {ean}")
            
            # Log page structure for debugging
            self.log_page_structure(soup, search_term, page)
            
        except Exception as e:
            log.error(f"Error extracting EANs from page: {e}")
        
        return eans
    
    def log_page_structure(self, soup, search_term, page):
        """Log page structure for debugging and improvement"""
        log.info(f"Page Structure Analysis for {search_term} page {page}:")
        
        # Check for product containers
        product_patterns = [
            ('div', re.compile(r'product', re.I)),
            ('article', re.compile(r'product', re.I)),
            ('li', re.compile(r'product', re.I)),
            ('div', re.compile(r'item', re.I)),
            ('div', re.compile(r'card', re.I))
        ]
        
        for tag, pattern in product_patterns:
            containers = soup.find_all(tag, class_=pattern)
            if containers:
                log.info(f"  Found {len(containers)} {tag} elements with product-like classes")
                if containers:
                    first = containers[0]
                    log.info(f"  First container classes: {first.get('class', [])}")
        
        # Check for pagination
        pagination = soup.find_all(['nav', 'div', 'ul'], class_=re.compile(r'pag', re.I))
        if pagination:
            log.info(f"  Found {len(pagination)} pagination elements")
        
        # Check for search results count
        results_text = soup.find_all(text=re.compile(r'résultat|produit|trouvé', re.I))
        if results_text:
            log.info(f"  Results info: {results_text[:2]}")
    
    def go_to_next_page(self):
        """Try to navigate to next page of results"""
        try:
            # Try different next page selectors
            next_selectors = [
                "//a[contains(text(), 'Suivant')]",
                "//button[contains(text(), 'Suivant')]",
                "//a[contains(@class, 'next')]",
                "//button[contains(@class, 'next')]",
                "//a[@aria-label='Next']",
                "//button[@aria-label='Next']"
            ]
            
            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.XPATH, selector)
                    if next_button.is_enabled():
                        next_button.click()
                        log.info("Clicked next page button")
                        return True
                except NoSuchElementException:
                    continue
            
            log.info("No next page button found")
            return False
            
        except Exception as e:
            log.error(f"Error going to next page: {e}")
            return False
    
    def is_valid_ean(self, ean_string):
        """Validate EAN format and checksum"""
        if not ean_string or not isinstance(ean_string, str):
            return False
        
        ean = ean_string.strip()
        if not re.match(r'^\d{13}$', ean):
            return False
        
        try:
            digits = [int(d) for d in ean]
            checksum = sum(digits[i] if i % 2 == 0 else digits[i] * 3 for i in range(12))
            return (10 - (checksum % 10)) % 10 == digits[12]
        except:
            return False
    
    def save_eans_to_file(self, eans, filename=None):
        """Save collected EANs to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"browser_collected_eans_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['EAN', 'Collection_Time'])
            
            for ean in sorted(eans):
                writer.writerow([ean, datetime.now().isoformat()])
        
        log.info(f"Saved {len(eans)} EANs to {filename}")
        return filename
    
    def cleanup(self):
        """Clean up browser resources"""
        if self.driver:
            self.driver.quit()
            log.info("Browser driver closed")

def main():
    """Main execution function"""
    collector = BrowserEANCollector(headless=False)  # Set to True for headless mode
    
    try:
        # Setup browser
        if not collector.setup_driver():
            log.error("Failed to setup browser driver")
            return
        
        # Load store mappings
        log.info("Loading store mappings...")
        stores = []
        try:
            with open('store_mappings_clean.csv', 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) >= 2:
                        store_id = row[0].strip().replace('\ufeff', '')
                        store_city = row[1].strip()
                        stores.append((store_id, store_city))
        except FileNotFoundError:
            log.error("store_mappings_clean.csv not found!")
            return
        
        log.info(f"Loaded {len(stores)} stores")
        
        # Use first working store for testing
        test_stores = stores[:3]  # Try first 3 stores

        for store_id, store_city in test_stores:
            log.info(f"Testing with store: {store_id} ({store_city})")

            try:
                if collector.navigate_to_store(store_id, store_city):
                    log.info("Store navigation successful, starting EAN collection...")

                    all_eans = set()
                    test_terms = collector.search_terms[:1]  # Test with first search term only

                    for search_term in test_terms:
                        log.info(f"\n{'='*50}")
                        log.info(f"SEARCHING FOR: {search_term}")
                        log.info(f"{'='*50}")

                        eans = collector.search_products(search_term, max_pages=1)
                        all_eans.update(eans)

                        log.info(f"Collected {len(eans)} EANs for '{search_term}'")
                        log.info(f"Total unique EANs: {len(all_eans)}")

                        # Save progress
                        if all_eans:
                            collector.save_eans_to_file(all_eans)

                        time.sleep(3)  # Delay between searches

                    # Final results
                    log.info(f"\n{'='*50}")
                    log.info(f"COLLECTION COMPLETE")
                    log.info(f"{'='*50}")
                    log.info(f"Total unique EANs collected: {len(all_eans)}")

                    if all_eans:
                        final_file = collector.save_eans_to_file(all_eans, "final_browser_ean_collection.csv")
                        log.info(f"Final collection saved to: {final_file}")

                        # Show sample EANs
                        sample_eans = list(all_eans)[:10]
                        log.info(f"Sample EANs: {sample_eans}")
                        break  # Success with this store
                    else:
                        log.warning("No EANs collected - check debug HTML files")
                        log.info("Continuing to next store...")
                else:
                    log.warning(f"Failed to navigate to store {store_id}, trying next store...")
                    continue

            except Exception as e:
                log.error(f"Error processing store {store_id}: {e}")
                collector.save_debug_html(f"error_store_{store_id}")
                continue
        
    except Exception as e:
        log.error(f"Unexpected error: {e}")
    finally:
        collector.cleanup()

if __name__ == "__main__":
    main()
