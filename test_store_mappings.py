#!/usr/bin/env python3
"""
Test EAN-based API approach with multiple stores using store mappings from City page.html
"""

import requests
import json
import time
from datetime import datetime

def test_store_context_establishment(store_id, city_code):
    """Test if we can establish store context for a given store"""
    session = requests.Session()

    # Headers from successful context.har analysis
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }

    try:
        # Step 1: Access store page to establish context
        store_url = f"https://www.intermarche.com/magasins/{store_id}/{city_code}/infos-pratiques"
        print(f"Testing store {store_id} with city code '{city_code}'")
        print(f"Accessing: {store_url}")

        response = session.get(store_url, headers=headers, timeout=10)
        print(f"Store page status: {response.status_code}")

        if response.status_code != 200:
            return False, f"Store page failed with status {response.status_code}"

        # Note: Skip shopping page check as many stores don't have online shopping
        # but may still work with the API
        return True, "Store context established successfully"

    except Exception as e:
        return False, f"Error: {str(e)}"

def test_ean_api_access(store_id):
    """Test EAN-based API access for a store"""
    session = requests.Session()
    
    # API headers from successful tests
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'x-red-version': '3',
        'x-itm-device-fp': 'desktop',
        'x-itm-session-id': f'session_{int(time.time())}',
        'x-custom-hash': f'{store_id}-disconnected-desktop',
        'Origin': 'https://www.intermarche.com',
        'Referer': f'https://www.intermarche.com/magasins/{store_id}/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    try:
        # Test suggestion API
        suggestion_url = f"https://www.intermarche.com/api/service/produits/v1/pdvs/{store_id}/products/suggestion"
        suggestion_data = {
            "query": "lait",
            "category": "1",
            "limit": 5
        }
        
        response = session.post(suggestion_url, headers=api_headers, json=suggestion_data, timeout=10)
        print(f"Suggestion API status: {response.status_code}")
        
        if response.status_code != 200:
            return False, f"Suggestion API failed with status {response.status_code}"
        
        suggestion_result = response.json()
        if not suggestion_result.get('suggestions'):
            return False, "No suggestions returned"
        
        # Extract EAN codes
        ean_codes = []
        for suggestion in suggestion_result['suggestions'][:3]:  # Test with first 3
            if 'ean' in suggestion:
                ean_codes.append(suggestion['ean'])
        
        if not ean_codes:
            return False, "No EAN codes found in suggestions"
        
        # Test byEans API
        byeans_url = f"https://www.intermarche.com/api/service/produits/v3/stores/{store_id}/products/byEans"
        byeans_data = {"eans": ean_codes}
        
        response = session.post(byeans_url, headers=api_headers, json=byeans_data, timeout=10)
        print(f"ByEans API status: {response.status_code}")
        
        if response.status_code != 200:
            return False, f"ByEans API failed with status {response.status_code}"
        
        byeans_result = response.json()
        products = byeans_result.get('products', [])
        
        if not products:
            return False, "No products returned from byEans API"
        
        return True, f"Successfully retrieved {len(products)} products"
        
    except Exception as e:
        return False, f"API Error: {str(e)}"

def main():
    """Test multiple stores from the store mappings"""
    
    # Load store mappings
    try:
        with open('store_mappings_clean.csv', 'r', encoding='utf-8') as f:
            store_mappings = []
            for line in f:
                line = line.strip()
                if line and ',' in line:
                    store_id, city_code = line.split(',', 1)
                    store_mappings.append((store_id, city_code))
    except FileNotFoundError:
        print("Error: store_mappings_clean.csv not found. Please run the extraction script first.")
        return
    
    print(f"Loaded {len(store_mappings)} store mappings")
    
    # Test a sample of stores - include our known working store and some others
    known_working = [('12118', 'marseille-13004')]
    test_stores = known_working + store_mappings[:9]  # Test known working + first 9 others (10 total)
    
    results = {
        'successful_stores': [],
        'failed_stores': [],
        'context_failures': [],
        'api_failures': []
    }
    
    for i, (store_id, city_code) in enumerate(test_stores, 1):
        print(f"\n=== Testing Store {i}/{len(test_stores)}: {store_id} ({city_code}) ===")
        
        # Test store context establishment
        context_success, context_message = test_store_context_establishment(store_id, city_code)
        print(f"Context: {context_message}")
        
        if not context_success:
            results['context_failures'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': context_message
            })
            results['failed_stores'].append(store_id)
            continue
        
        # Test EAN API access
        api_success, api_message = test_ean_api_access(store_id)
        print(f"API: {api_message}")
        
        if not api_success:
            results['api_failures'].append({
                'store_id': store_id,
                'city_code': city_code,
                'error': api_message
            })
            results['failed_stores'].append(store_id)
        else:
            results['successful_stores'].append({
                'store_id': store_id,
                'city_code': city_code
            })
        
        # Longer delay between stores to avoid rate limiting
        time.sleep(3)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"store_mappings_test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total stores tested: {len(test_stores)}")
    print(f"Successful stores: {len(results['successful_stores'])}")
    print(f"Failed stores: {len(results['failed_stores'])}")
    print(f"Context failures: {len(results['context_failures'])}")
    print(f"API failures: {len(results['api_failures'])}")
    print(f"Success rate: {len(results['successful_stores'])/len(test_stores)*100:.1f}%")
    print(f"\nResults saved to: {results_file}")
    
    if results['successful_stores']:
        print(f"\nWorking stores:")
        for store in results['successful_stores'][:10]:  # Show first 10
            print(f"  - {store['store_id']} ({store['city_code']})")
    
    if results['context_failures']:
        print(f"\nContext failures (first 5):")
        for failure in results['context_failures'][:5]:
            print(f"  - {failure['store_id']} ({failure['city_code']}): {failure['error']}")

if __name__ == "__main__":
    main()
