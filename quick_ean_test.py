#!/usr/bin/env python3
"""Quick EAN test for debugging"""

import requests
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def test_single_ean():
    """Test a single EAN on one store to debug response structure"""
    
    store_id = "12118"
    store_name = "Super Marseille"
    store_city = "marseille-13004"
    target_ean = "3250390678885"
    
    session = requests.Session()
    base_url = "https://www.intermarche.com"
    store_url = f"{base_url}/magasins/{store_id}/{store_city}/infos-pratiques"
    
    # Basic headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }
    
    log.info(f"🏪 Testing store {store_id} with EAN {target_ean}")
    
    # Step 1: Store page
    log.info("📍 Getting store page...")
    response = session.get(store_url, headers=headers)
    log.info(f"Store page: {response.status_code}")

    # Step 2: GET accueil
    log.info("🏠 GET accueil...")
    accueil_url = f"{base_url}/accueil"
    get_headers = headers.copy()
    get_headers['Referer'] = store_url
    response = session.get(accueil_url, headers=get_headers)
    log.info(f"GET Accueil: {response.status_code}")

    # Step 3: POST accueil
    log.info("🚀 POST accueil...")
    post_headers = headers.copy()
    post_headers.update({
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': base_url,
        'Referer': accueil_url,
        'next-action': '8b5b8b7b8b5b8b7b8b5b8b7b8b5b8b7b8b5b8b7b',
        'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%22magasin%22%2C%7B%22children%22%3A%5B%22%5Bslug%5D%22%2C%7B%22children%22%3A%5B%22accueil%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%5D%7D%5D%7D%5D%7D%5D%7D%5D',
    })

    response = session.post(accueil_url, headers=post_headers, data='[]')
    log.info(f"POST Accueil: {response.status_code}")
    
    # Step 4: Test byEans API
    log.info(f"🎯 Testing EAN {target_ean}")
    
    api_headers = headers.copy()
    api_headers.update({
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'Origin': base_url,
        'Referer': f'{base_url}/accueil',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'x-itm-device-fp': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
        'x-itm-session-id': 'ed4360dc-2dce-4a3a-b7f3-3ea75d9fa518',
        'x-red-device': 'red_fo_desktop',
        'x-red-version': '3',
        'x-service-name': 'produits',
        'x-optional-oauth': 'true',
        'x-is-server': 'false'
    })
    
    byeans_data = {"eans": [target_ean]}
    
    response = session.post(
        f"{base_url}/api/service/produits/v3/stores/{store_id}/products/byEans",
        json=byeans_data,
        headers=api_headers,
        timeout=30
    )
    
    log.info(f"🛍️ byEans API: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            log.info(f"✅ Response type: {type(data)}")
            log.info(f"✅ Response content: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except Exception as e:
            log.error(f"❌ JSON decode error: {e}")
            log.info(f"Raw response: {response.text[:500]}")
    else:
        log.error(f"❌ API failed: {response.status_code}")
        log.info(f"Response: {response.text[:500]}")

if __name__ == "__main__":
    test_single_ean()
